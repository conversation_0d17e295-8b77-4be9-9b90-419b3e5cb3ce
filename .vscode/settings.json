{"typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "stylelint.validate": ["typescript", "typescriptreact", "css", "less"], "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[go]": {"editor.defaultFormatter": "golang.go"}, "[less]": {"editor.defaultFormatter": "vscode.css-language-features"}}
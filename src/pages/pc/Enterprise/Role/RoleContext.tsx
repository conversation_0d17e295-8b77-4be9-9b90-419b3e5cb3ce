import React from 'react';

import { RoleList } from './components/RoleList';
import type { RoleListProps } from './components/RoleList/RoleList';
import type { ModalState } from './components/RoleModal/RoleModal';
import { UserTable } from './components/UserTable';
import type { UserTableProps } from './components/UserTable/UserTable';
import styles from './RoleContext.less';

export interface RoleContextProps {
  table: UserTableProps;
  role: Omit<RoleListProps, 'onOpenModal'>;
  onOpenModal: (
    state: ModalState,
    data?: {
      id: number;
      name?: string;
    },
  ) => void;
}

export const RoleContext = (props: RoleContextProps) => {
  const { table, role, onOpenModal } = props;

  return (
    <div className={styles.roleContextTable}>
      <RoleList {...role} onOpenModal={onOpenModal} />
      <UserTable {...table} />
    </div>
  );
};

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import type { OrgItemType } from '@/components/OrgSelectCard/type';
import type { UserOrgSelectorProps } from '@/components/UserOrgTree/UserOrgSelector';
import { MeContext } from '@/contexts/me';
import { MessageContext } from '@/contexts/MessageProvider';
import { useOrgSelect } from '@/hooks/useOrgSelect';
import { fm2 } from '@/modules/Locale';
import { formatterListItem } from '@/utils/orgSelect';

import { formatterOpenSelectedList, MAX_SELECTED_NUMBER } from '../../Dissolve/utils';
import styles from '../components/BindModal/BindModal.less';
import { transferTeam } from '../service/api';

export const SELECT_BASIC = {
  itemHeight: 36,
  itemKey: 'id',
  itemRender: formatterListItem,
};

const HEIGHT_WITH_SUBTITLE = 488;

export const useDevolved = (): {
  open: boolean;
  org: UserOrgSelectorProps;
  onClose: () => void;
  handleDevolvedEnterprise: () => void;
  openModal: () => void;
} => {
  // 移交企业modal开启状态
  const [open, setOpen] = useState<boolean>(false);
  const me = useContext(MeContext);
  const message = useContext(MessageContext);

  const {
    loading,
    crumbs,
    orgList,
    selectedList,
    initSelect,
    clearAllSelected,
    onScroll,
    clearOrgList,
    onSearch,
    clearSearch,
    searchResult,
    searchLoading,
  } = useOrgSelect({
    pageSize: 50,
    checkDepartment: false,
    searchUserConfig: {
      includeDisabledMember: false,
    },
    ignorePending: true,
    admin: true,
  });

  const openModal = useCallback(() => {
    initSelect([]);
    setOpen(true);
  }, [initSelect]);

  const onResetModal = useCallback(() => {
    clearAllSelected();
    clearOrgList();
    clearSearch();
    setOpen(false);
  }, [clearAllSelected, clearOrgList, clearSearch]);

  const orgDataList: OrgItemType[] = useMemo(() => {
    return orgList.map((item) => ({
      ...item,
      disabled: item.id === me?.id,
    }));
  }, [orgList, me?.id]);

  const searchResultList: OrgItemType[] | undefined = useMemo(() => {
    return searchResult?.map((item) => ({
      ...item,
      disabled: item.id === me?.id,
    }));
  }, [searchResult, me?.id]);

  const org = {
    height: HEIGHT_WITH_SUBTITLE,
    select: {
      loading: loading || searchLoading,
      onSearch,
      title: fm2('Role.selectApp'),
      placeholder: fm2('Role.search'),
      breadcrumb: crumbs
        ? {
            crumbs,
          }
        : undefined,
      search: searchResultList
        ? {
            ...SELECT_BASIC,
            data: searchResultList,
          }
        : undefined,
      org: {
        ...SELECT_BASIC,
        onScroll,
        data: orgDataList,
      },
    },
    selected: {
      header: {
        title: (
          <>
            <div className={styles.overflowEllipsisDiv}>{fm2('Role.selectedMember')}</div>
            <>({selectedList?.length ?? 0})</>
          </>
        ),
        onPress: clearAllSelected,
        pressText: fm2('Role.clear'),
        disabled: selectedList?.length === 0 || loading,
      },
      list: selectedList,
      itemRender: formatterOpenSelectedList,
    },
  };

  // 移交企业
  const handleDevolvedEnterprise = useCallback(() => {
    Modal.confirm({
      icon: <ExclamationCircleOutlined />,
      title: fm2('Role.transferEnterprise'),
      content: fm2('Role.transferEnterpriseTips'),
      okText: fm2('Role.transferEnterpriseConfirm'),
      cancelText: fm2('Role.cancel'),
      centered: true,
      okButtonProps: {
        danger: true,
      },
      maskClosable: false,
      onOk: () => {
        // 被转移的企业成员
        const { id: uid } = selectedList?.[0] ?? {};
        if (!uid) {
          return;
        }
        transferTeam(uid)
          .then(() => {
            message?.success(fm2('hanover.operationSuccess'), 2, () => {
              location.href = '/enterprise/settings';
            });
          })
          .catch(() => {
            message?.error(fm2('Members.operationFailed'));
          });
      },
      footer: (_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      },
    });
  }, [message, selectedList]);

  useEffect(() => {
    if (org.selected.list.length > MAX_SELECTED_NUMBER) {
      message?.open({
        key: 'max-warning',
        type: 'warning',
        content: fm2('Role.maxTransferNumber', { max: MAX_SELECTED_NUMBER }),
        duration: 2,
      });
    }
  }, [org.selected.list.length, message]);

  // 返回状态和操作
  return {
    open,
    org,
    handleDevolvedEnterprise,
    openModal,
    onClose: onResetModal,
  };
};

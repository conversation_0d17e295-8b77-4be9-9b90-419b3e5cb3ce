import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, message, Tooltip } from 'antd';
import type { ItemType } from 'antd/es/menu/interface';
import modal from 'antd/es/modal';
import useModal from 'antd/es/modal/useModal';
import debounce from 'lodash/debounce';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import EnterpriseStandardPng from '@/assets/premium/<EMAIL>';
import EnterprisePremiumPng from '@/assets/premium/<EMAIL>';
import { UserItem } from '@/components/UserItem';
import { PermissionContext } from '@/contexts/permissions';
import { fm2 } from '@/modules/Locale';

import { MenuKey } from '../components/RoleList/RoleItem';
import type { RoleListProps } from '../components/RoleList/RoleList';
import style from '../components/RoleList/RoleList.less';
import { ModalState } from '../components/RoleModal/RoleModal';
import type { ButtonProps, UserItemProps, UserTableProps } from '../components/UserTable/UserTable';
import styles from '../components/UserTable/UserTable.less';
import { deleteRole, deleteRoleMembers, getRoleMembers, getRoles } from '../service/api';
import { ADMIN_ROLE_ID, CREATOR_ROLE_ID, WHOLE_MEMBER_ROLE_ID } from '../service/type';
import { onUpgrade } from '../utils';
interface RoleProps {
  onOpenModal: (
    state: ModalState,
    data?: {
      id: number;
      name?: string;
    },
  ) => void;
  onOpenBindModal: () => void;
  openDevolvedEnterpriseModal: () => void;
}

interface UseRoleType {
  role: Omit<RoleListProps, 'onOpenModal'> & {
    tableTitle: string;
    modalContextHolder: any;
  };
  table: UserTableProps & {
    getUserList: (id: number) => void;
  };
}

const DEFAULT_HEIGHT = 362;

// 触底防抖时长
const DEBOUNCE_TIME = 1000;
// 表格行高-加载距离
const ROW_HEIGHT = 45;

export const useRole = (props: RoleProps): UseRoleType => {
  const { onOpenModal, onOpenBindModal, openDevolvedEnterpriseModal } = props;
  const permission = useContext(PermissionContext);
  const [modalContextHolder] = useModal();
  const [roleList, setRoleList] = useState<RoleListProps['roleList']>([]);
  const [actionKey, setActionKey] = useState<number>(CREATOR_ROLE_ID);
  const [userList, setUserList] = useState<UserItemProps[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [scrollY, setScrollY] = useState<number>(window.innerHeight - DEFAULT_HEIGHT);
  const [next, setNext] = useState<string>('');
  // const fm2 = useFormatMessage;
  /**
   * 禁用新建按钮
   */
  const disableCreateRole = useMemo(() => {
    // return !permission?.features?.includes('create_role_permission');
    return false;
  }, [permission]);

  /**
   * 禁用绑定按钮
   */
  const disableBindUser = useMemo(() => {
    // return !permission?.features?.includes('bind_member_permission');
    return false;
  }, [permission]);

  /**
   * 禁用修改按钮
   */
  const disableEditRole = useMemo(() => {
    // return !permission?.features?.includes('modifying_role_permission');
    return false;
  }, [permission]);

  /**
   * 获取该角色的成员列表
   */
  const getUserList = useCallback(async (id: number, nextCode?: string, flag?: boolean) => {
    if (id === WHOLE_MEMBER_ROLE_ID) {
      return false;
    }
    setNext('');
    setLoading(true);
    try {
      const res = await getRoleMembers(id, nextCode);
      if (!flag) {
        setUserList((prevUserList) => {
          return nextCode ? [...prevUserList, ...res.users] : res.users;
        });
        setNext(!res?.next || res.users.length < 50 ? '' : res.next);
      } else {
        return res.users.length > 0;
      }
    } catch (error) {
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 初始化角色列表
   */
  const initRoleList = useCallback(() => {
    getRoles().then((list) => {
      const arr = list.map((item: { id: number }) => {
        const newItem = {
          ...item,
          items: [] as ItemType[],
        };
        // 部分id是固定死的，针对这些id有部份业务逻辑
        // 比如：创建者角色不能修改和删除，管理员和全体成员角色不能删除
        if (item.id === CREATOR_ROLE_ID) {
          newItem.items = [
            {
              key: MenuKey.Preview,
              label: fm2('Role.viewPermission'),
            },
          ];
        } else if (item.id === ADMIN_ROLE_ID || item.id === WHOLE_MEMBER_ROLE_ID) {
          newItem.items = [
            {
              key: MenuKey.Preview,
              label: fm2('Role.viewPermission'),
            },
            ...(disableEditRole
              ? []
              : [
                  {
                    key: MenuKey.Edit,
                    label: fm2('Role.modifyPermission'),
                    disabled: disableEditRole,
                  },
                ]),
          ];
        } else {
          newItem.items = [
            {
              key: MenuKey.Preview,
              label: fm2('Role.viewPermission'),
            },
            ...(disableEditRole
              ? []
              : [
                  {
                    key: MenuKey.Edit,
                    label: fm2('Role.modifyPermission'),
                    disabled: disableEditRole,
                  },
                ]),
            {
              key: MenuKey.Divider,
              type: 'divider',
            },
            {
              key: MenuKey.Delete,
              label: fm2('Role.deleteRole'),
              danger: true,
            },
          ];
        }
        return newItem;
      });
      setRoleList(arr);
    });
  }, [disableEditRole]);

  /**
   * 切换角色
   */
  const onPress = useCallback(
    (id: number) => {
      if (id !== actionKey) {
        setActionKey(id);
        getUserList(id);
        setSelectedRowKeys([]);
      }
    },
    [getUserList, actionKey],
  );

  /**
   * 创建角色
   */
  const onCreateRole = useCallback(() => {
    onOpenModal(ModalState.Create);
  }, [onOpenModal]);

  /**
   * 删除角色
   */
  const onDeleteRole = useCallback(
    async (id: number) => {
      const userListLenght = await getUserList(id, '', true);
      if (userListLenght) {
        modal.confirm({
          title: fm2('Role.cannotDeleteRole'),
          content: fm2('Role.pleaseRemoveAllMembersBeforeDeletingRole'),
          // icon: <ExclamationCircleOutlined />,
          cancelText: fm2('Role.confirm'),
          cancelButtonProps: {
            type: 'primary',
          },
          centered: true,
          maskClosable: false,
          footer: (_, { CancelBtn }) => {
            return <CancelBtn />;
          },
        });
      } else {
        modal.confirm({
          title: fm2('Role.deleteRole'),
          content: (
            <>
              {fm2('Role.deleteRoleConfirm')}
              <br />
              {fm2('Role.deleteRoleTip')}
            </>
          ),
          okText: fm2('Role.deleteConfirm'),
          okButtonProps: {
            danger: true,
          },
          cancelText: fm2('Role.cancel'),
          icon: <ExclamationCircleOutlined />,
          onOk: () => {
            deleteRole(id)
              .then(() => {
                message.success(fm2('Role.deleteSuccess'));
                const newRoleList = roleList.filter((item) => item.id !== id);
                setRoleList(newRoleList);
                if (actionKey === id) {
                  const newActionKey = newRoleList[newRoleList.length - 1].id!;
                  setActionKey(newActionKey);
                  getUserList(newActionKey);
                }
              })
              .catch(() => {
                message.error(fm2('Role.deleteError'));
              });
          },
          centered: true,
          maskClosable: false,
          footer: (_, { OkBtn, CancelBtn }) => {
            return (
              <>
                <OkBtn />
                <CancelBtn />
              </>
            );
          },
        });
      }
    },
    [modal, roleList, userList, getUserList],
  );

  /**
   * 修改选中成员
   */
  const onChangeSelectedRowKeys = useCallback(
    (list: number[]) => {
      setSelectedRowKeys(list);
    },
    [setSelectedRowKeys],
  );

  /**
   * 移除成员
   */
  const onRemoveMember = useCallback(
    (id: number) => {
      modal.confirm({
        title: fm2('Role.removeMember'),
        content: (
          <>
            {fm2('Role.removeMemberConfirm')}
            <br />
            {fm2('Role.removeMemberTip')}
          </>
        ),
        okText: fm2('Role.removeMemberConfirmTitle'),
        okButtonProps: {
          danger: true,
        },
        cancelText: fm2('Role.cancel'),
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          deleteRoleMembers(actionKey, [id]).then(() => {
            message.success(fm2('Role.removeMemberSuccess'));
            getUserList(actionKey);
            setSelectedRowKeys([]);
          });
        },
        centered: true,
        maskClosable: false,
        footer: (_, { OkBtn, CancelBtn }) => {
          return (
            <>
              <OkBtn />
              <CancelBtn />
            </>
          );
        },
      });
    },
    [actionKey, getUserList],
  );

  /**
   * 滚动加载
   */
  const handleTableScroll: React.UIEventHandler<HTMLDivElement> = useCallback(
    debounce((e) => {
      if (!next) {
        return; // 加载完毕
      }
      const { scrollTop, clientHeight } = e.target as HTMLDivElement;
      const { childNodes } = e.target as HTMLDivElement;
      const innerTableListHeight = (childNodes[0] as HTMLDivElement).clientHeight;
      if (innerTableListHeight - ROW_HEIGHT < scrollTop + clientHeight) {
        getUserList(actionKey, next);
      }
    }, DEBOUNCE_TIME),
    [getUserList, next],
  );

  /**
   * 表格标题
   */
  const tableTitle = useMemo(() => {
    const selectedRole = roleList.find((item) => item.id === actionKey);
    return selectedRole ? selectedRole.name : '';
  }, [roleList, actionKey]);

  /**
   * 按钮列表
   */
  const buttons: ButtonProps[] = useMemo(() => {
    // 查看权限
    const viewPermission = {
      id: 'view-permission',
      text: fm2('Role.viewPermission'),
      onPress: () => {
        onOpenModal(ModalState.Preview, {
          id: actionKey,
          name: tableTitle,
        });
      },
    };

    // 修改权限
    const editRole = {
      id: 'edit-permission',
      text: fm2('Role.modifyPermission'),
      onPress: () => {
        if (disableEditRole) {
          onUpgrade();
        } else {
          onOpenModal(ModalState.Edit, {
            id: actionKey,
            name: tableTitle,
          });
        }
      },
      tooltip: disableEditRole ? fm2('Role.modifyPermissionTitle') : undefined,
      icon: disableEditRole ? (
        <img className={style.iconImg} src={EnterprisePremiumPng} onClick={onUpgrade} />
      ) : undefined,
    };

    // 绑定用户
    const bindUser = {
      id: 'bind-user',
      text: fm2('Role.bindUser'),
      onPress: () => {
        if (disableBindUser) {
          onUpgrade();
        } else {
          onOpenBindModal();
        }
      },
      type: 'primary',
      tooltip: disableBindUser ? fm2('Role.bindUserTitle') : undefined,
      icon: disableBindUser ? (
        <img className={style.iconImg} src={EnterpriseStandardPng} onClick={onUpgrade} />
      ) : undefined,
    };

    // 部分id是固定死的，针对这些id有部份业务逻辑
    // 比如：创建者角色不能修改和删除，管理员和全体成员角色不能删除
    if (actionKey === CREATOR_ROLE_ID) {
      return [viewPermission];
    } else if (actionKey === ADMIN_ROLE_ID) {
      return [bindUser, viewPermission, editRole];
    } else if (actionKey === WHOLE_MEMBER_ROLE_ID) {
      return [viewPermission, editRole];
    } else {
      return [bindUser, viewPermission, editRole];
    }
  }, [actionKey, tableTitle, disableBindUser, disableEditRole, onOpenModal, onOpenBindModal, onUpgrade]);

  /**
   * 扩展按钮列表
   */
  const expansionButtons: ButtonProps[] = useMemo(() => {
    if (actionKey === CREATOR_ROLE_ID || actionKey === WHOLE_MEMBER_ROLE_ID) {
      return [];
    } else {
      return [
        {
          id: 'batch-deletion',
          text: fm2('Role.batchDelete'),
          danger: true,
          disabled: selectedRowKeys.length === 0,
          onPress: () => {
            modal.confirm({
              title: fm2('Role.removeMember'),
              content: (
                <>
                  {fm2('Role.removeMemberRoleCountnt', { length: selectedRowKeys.length })}
                  <br />
                  {fm2('Role.removeMemberRoleTip')}
                </>
              ),
              okText: fm2('Role.removeMemberConfirmTitle'),
              okButtonProps: {
                danger: true,
              },
              cancelText: fm2('Role.cancel'),
              icon: <ExclamationCircleOutlined />,
              onOk: () => {
                deleteRoleMembers(actionKey, selectedRowKeys).then(() => {
                  message.success(fm2('Role.removeMemberSuccess'));
                  setSelectedRowKeys([]);
                  getUserList(actionKey);
                });
              },
              centered: true,
              maskClosable: false,
              footer: (_, { OkBtn, CancelBtn }) => {
                return (
                  <>
                    <OkBtn />
                    <CancelBtn />
                  </>
                );
              },
            });
          },
        },
      ];
    }
  }, [actionKey, userList, modal, selectedRowKeys, getUserList]);

  /**
   * 表格列
   */
  const columns: TableColumnsType<UserItemProps> = useMemo(
    () => [
      {
        title: fm2('EfficiencyPanel.member'),
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        render: (_, record) => <UserItem {...record} />,
      },
      {
        title: fm2('Watermark.email'),
        dataIndex: 'email',
        key: 'email',
        ellipsis: true,
        render: (_, record) => {
          return (
            <Tooltip placement="topLeft" title={record.email}>
              <div className={styles.email}>{record.email}</div>
            </Tooltip>
          );
        },
      },
      {
        title: fm2('Members.operation'),
        dataIndex: 'id',
        key: 'id',
        width: 200,
        render: (id: number) =>
          actionKey === CREATOR_ROLE_ID ? (
            <span className={style.dangerButton} onClick={openDevolvedEnterpriseModal}>
              {fm2('Role.transferEnterprise')}
            </span>
          ) : (
            <Button onClick={() => onRemoveMember(id)}>{fm2('Role.removeMemberRole')}</Button>
          ),
      },
    ],
    [modal, onRemoveMember, openDevolvedEnterpriseModal, actionKey],
  );

  // table动态高度
  useEffect(() => {
    const handleResize = () => {
      setScrollY(Math.max(window.innerHeight - DEFAULT_HEIGHT, 0));
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    initRoleList();
    getUserList(CREATOR_ROLE_ID);
  }, [initRoleList, getUserList]);

  return {
    role: {
      title: fm2('Role.roleList'),
      roleList,
      button: {
        title: fm2('Role.newRole'),
        onPress: disableCreateRole ? onUpgrade : onCreateRole,
        tooltip: disableCreateRole ? fm2('Role.upgradeRole') : undefined,
        icon: disableCreateRole ? (
          <img className={style.iconImg} src={EnterprisePremiumPng} onClick={onUpgrade} />
        ) : undefined,
      },
      actionKey,
      tableTitle,
      onPress,
      initRoleList,
      onDeleteRole,
      modalContextHolder,
    },
    table: {
      buttons,
      expansionButtons,
      userList,
      title: tableTitle,
      columns,
      actionKey,
      tableLoading: loading,
      onChangeSelectedRowKeys,
      getUserList,
      scrollY,
      handleTableScroll,
    },
  };
};

import React, { useCallback, useContext, useMemo, useState } from 'react';

import type { OrgItemType } from '@/components/OrgSelectCard/type';
import { MessageContext } from '@/contexts/MessageProvider';
import { useOrgSelect } from '@/hooks/useOrgSelect';
import { fm2 } from '@/modules/Locale';
import { formatterListItem } from '@/utils/orgSelect';

import styles from '../components/BindModal/BindModal.less';
import { bindRoleMembers } from '../service/api';
import { formatterOpenSelectedList } from '../utils';
export const SELECT_BASIC = {
  itemHeight: 36,
  itemKey: 'id',
  itemRender: formatterListItem,
};

const HEIGHT_WITH_SUBTITLE = 488;

export const useBindModal = () => {
  const [open, setOpen] = useState<boolean>(false);
  const message = useContext(MessageContext);

  const {
    loading,
    crumbs,
    orgList,
    selectedList,
    initSelect,
    clearAllSelected,
    onScroll,
    clearOrgList,
    onSearch,
    clearSearch,
    searchResult,
    searchLoading,
  } = useOrgSelect({
    pageSize: 50,
    checkDepartment: false,
    ignorePending: true,
    admin: true,
  });

  const orgDataList: OrgItemType[] = useMemo(() => {
    return orgList.map((item) => ({
      ...item,
      disabled: item.teamRole === 'creator',
    }));
  }, [orgList]);

  const org = {
    height: HEIGHT_WITH_SUBTITLE,
    select: {
      loading: loading || searchLoading,
      onSearch,
      title: fm2('Role.selectMember'),
      placeholder: fm2('Role.search'),
      breadcrumb: crumbs
        ? {
            crumbs,
          }
        : undefined,
      search: searchResult
        ? {
            ...SELECT_BASIC,
            data: searchResult,
          }
        : undefined,
      org: {
        ...SELECT_BASIC,
        onScroll,
        data: orgDataList,
      },
    },
    selected: {
      header: {
        title: (
          <>
            <div className={styles.overflowEllipsisDiv}>{fm2('Role.addedMember')}</div>
            <>({selectedList?.length ?? 0})</>
          </>
        ),
        onPress: clearAllSelected,
        pressText: fm2('Role.clear'),
        disabled: selectedList?.length === 0 || loading,
      },
      list: selectedList,
      itemRender: formatterOpenSelectedList,
    },
  };

  const handleCloseModal = useCallback(() => {
    setOpen(false);
    clearSearch();
    clearOrgList();
    clearAllSelected();
  }, [clearSearch, clearOrgList, clearAllSelected]);

  const onOpenBindModal = useCallback(() => {
    setOpen(true);
    initSelect([]);
  }, [initSelect]);

  const handleBindModal = useCallback(
    (id: number, initUserList: (id: number) => void) => {
      const userIds = selectedList.map((item) => item.id);
      bindRoleMembers(id, userIds)
        .then(() => {
          message?.success(fm2('hanover.operationSuccess'));
          handleCloseModal();
          initUserList(id);
        })
        .catch((err) => {
          if (err?.error?.code === 'CANT_MODIFY_DEFAULT_ROLE') {
            message?.error(fm2('Role.defaultRole'));
          } else {
            message?.error(fm2('Members.operationFailed'));
          }
        });
    },
    [message, selectedList, handleCloseModal],
  );

  return {
    org,
    open,
    onClose: handleCloseModal,
    onOpenBindModal,
    handleBindModal,
  };
};

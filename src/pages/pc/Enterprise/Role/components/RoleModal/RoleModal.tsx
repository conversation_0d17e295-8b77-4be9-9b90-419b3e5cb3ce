import { Empty, Input, Modal } from 'antd';
import React, { useCallback } from 'react';

import type { PermissionItemKeys } from '@/contexts/permissions/type';
import { fm2 } from '@/modules/Locale';

import type { RolePermissionItemsItem } from './RoleItem';
import { RoleItem } from './RoleItem';
import styles from './RoleModal.less';
// import {
//   StyledEmpty,
//   StyledInput,
//   StyledModalContent,
//   StyledModalItemTitle,
//   StyledRoleModal,
//   StyledRoles,
// } from './RoleModal.styled';

export enum ModalState {
  Edit = 'edit',
  Preview = 'preview',
  Create = 'create',
}

export interface ModalDataProps {
  canModifyName: boolean;
  rolePermissionItems: RolePermissionItemsItem[];
  roleName: string;
}

export interface RoleModalProps {
  open: boolean;
  modalState?: ModalState;
  modalData: ModalDataProps;
  onChangeRoleName: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onChangeSelectedPermissions: (key: PermissionItemKeys) => void;
  onCancel: () => void;
  onSubmit: (init?: () => void) => void;
  init?: () => void;
  modalLoading: boolean;
  title: string;
}

export const RoleModal = (props: RoleModalProps) => {
  const {
    open,
    modalState,
    modalData,
    onChangeRoleName,
    onChangeSelectedPermissions,
    onCancel,
    onSubmit,
    init,
    modalLoading,
    title,
  } = props;
  const { canModifyName, rolePermissionItems, roleName } = modalData;

  const handleSubmit = useCallback(() => {
    onSubmit(init);
  }, [onSubmit, init]);

  return (
    <Modal
      cancelText={fm2('Role.cancel')}
      centered={true}
      footer={(_, { OkBtn, CancelBtn }) => {
        if (modalState === ModalState.Preview) {
          return <OkBtn />;
        }
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      loading={modalLoading}
      maskClosable={false}
      okButtonProps={{
        disabled: modalLoading || !roleName,
      }}
      okText={modalState === ModalState.Preview ? fm2('hanover.close') : fm2('Role.confirm')}
      open={open}
      title={title}
      width={618}
      onCancel={onCancel}
      onOk={modalState === ModalState.Preview ? onCancel : handleSubmit}
    >
      <div className={styles.modalContent}>
        {canModifyName ? (
          <>
            <div className={styles.modalItemTitle}>{fm2('Role.roleName')}</div>
            <Input className={styles.input} maxLength={32} value={roleName} onChange={onChangeRoleName} />
            <div className={styles.modalItemTitle}>{fm2('Role.rolePermission')}</div>
          </>
        ) : null}
        <div className={styles.roles}>
          {rolePermissionItems.map((item) => (
            <RoleItem {...item} key={item.keyId} onChangeSelectedPermissions={onChangeSelectedPermissions} />
          ))}
          {modalState === ModalState.Preview && rolePermissionItems.length === 0 && (
            <Empty className={styles.empty} description={fm2('Role.noPermission')} />
          )}
        </div>
      </div>
    </Modal>
  );
};

import { But<PERSON>, Checkbox, Popover, Tooltip } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { useCallback, useMemo } from 'react';

import EnterprisePng from '@/assets/premium/<EMAIL>';
import EnterprisePremiumPng from '@/assets/premium/<EMAIL>';
import { ReactComponent as InfoSvgIcon } from '@/assets/role/info.svg';
import type { PermissionItemKeys } from '@/contexts/permissions/type';
import { fm } from '@/modules/Locale';

import { getGuideImgInLang } from '../../utils';
import styles from './RoleItem.less';
export enum RoleIconType {
  Enterprise = 'Enterprise',
  EnterprisePremium = 'EnterprisePremium',
}

export interface RolePermissionItemsItem {
  keyId: PermissionItemKeys;
  name: string;
  description: string;
  disabled?: boolean;
  optional?: boolean;
  checked?: boolean;
  checkedTooltip?: string;
  icon?: RoleIconType;
}

interface RoleItemProps extends RolePermissionItemsItem {
  onChangeSelectedPermissions: (keyId: PermissionItemKeys) => void;
}

export const RoleItem = (props: RoleItemProps) => {
  const { onChangeSelectedPermissions, keyId, name, optional, checked, icon, description, checkedTooltip, disabled } =
    props;

  const iconSrc = useMemo(() => {
    return icon === RoleIconType.EnterprisePremium ? EnterprisePremiumPng : EnterprisePng;
  }, [icon]);

  const onChangeCheckbox = useCallback(
    (e: CheckboxChangeEvent) => {
      onChangeSelectedPermissions(e.target.value);
    },
    [onChangeSelectedPermissions],
  );

  const onUpgrade = useCallback(() => {
    // window.open(
    //   // `${RoutePaths.Billing}?dialog=purchase_edition&paymentSource=${EnterprisePaymentSource.ManageRole}`,
    // );
  }, []);

  const Context = useCallback(() => {
    return (
      <div className={styles.checkboxContent}>
        {name}
        <div className={styles.iconContainer}>
          {icon ? (
            <Popover
              content={
                <>
                  <div className={styles.popoverDesc}>{description}</div>
                  <img className={styles.guideImg} src={getGuideImgInLang(keyId) ?? ''} />
                  <Button block type="primary" onClick={onUpgrade}>
                    {fm('Role.upgradeUse')}
                  </Button>
                </>
              }
              overlayInnerStyle={{ width: 260, padding: 12 }}
              placement="bottom"
              title={
                <div className={styles.flexDiv}>
                  <div className={styles.popoverName}>{name}</div>
                  <img className={styles.styledImg} src={iconSrc} />
                </div>
              }
            >
              <img className={styles.styledImg} src={iconSrc} />
            </Popover>
          ) : (
            <Tooltip placement="top" title={description}>
              <InfoSvgIcon />
            </Tooltip>
          )}
        </div>
      </div>
    );
  }, [name, icon, description, onUpgrade, iconSrc, description]);

  return (
    <div className={styles.roleDiv}>
      {optional ? (
        <div className={styles.flexDiv}>
          <Tooltip placement="top" title={checkedTooltip}>
            <Checkbox
              checked={checked}
              disabled={disabled}
              style={{ marginRight: 12 }}
              value={keyId}
              onChange={onChangeCheckbox}
            />
          </Tooltip>
          <Context />
        </div>
      ) : (
        <Context />
      )}
    </div>
  );
};

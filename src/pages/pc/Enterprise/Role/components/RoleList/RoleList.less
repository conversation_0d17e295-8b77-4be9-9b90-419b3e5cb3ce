.roleList {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  width: 300px;
  padding: 0 16px;
  box-sizing: border-box;
  border-radius: 8px 0 0 8px;
  border-right: 1px solid var(--theme-separator-color-lighter);

  /* 创建新的滚动上下文 */
  -webkit-contain: strict; /* Safari/iOS 15.4+, Chrome/Android 52+ */
  contain: strict;
}

.titleDiv {
  display: flex;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  color: var(--interface-color-text-primary);
  font-size: 16px;
  font-weight: 500;
  line-height: 56px;
}

.listDiv {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

.bottomDiv {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0 24px;
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.styledButton {
  .ant-btn {
    border-radius: 2px;
    border: 1px solid var(--theme-separator-color-lighter);
    padding: 6px 15px;
    line-height: 24px;
    height: 38px;
  }
}

.iconImg {
  width: 18px;
  height: 18px;
  display: block;
  cursor: pointer;
}

.dangerButton {
  color: var(--theme-button-color-alert);
  cursor: pointer;
  line-height: 20px;
}

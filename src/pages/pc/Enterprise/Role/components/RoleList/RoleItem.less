.roleItem {
  margin-top: 4px;
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  background: var(--theme-layout-color-bg-white);
  font-weight: 400;

  &.action {
    background: var(--theme-text-button-color-active);
    font-weight: 500;
  }

  &:hover {
    background: var(--theme-layout-color-bg-white);

    &.action {
      background: var(--theme-text-button-color-active);
    }
  }

  .ant-dropdown .ant-dropdown-menu {
    border-radius: 4px;
  }

  .ant-dropdown
    .ant-dropdown-menu
    .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled) {
    color: var(--theme-text-color-alert);
  }

  .ant-dropdown
    .ant-dropdown-menu
    .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled):hover {
    color: var(--theme-text-color-alert);
    background: var(--theme-menu-color-bg-hover);
  }
}

.nameDiv {
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 24px;
  max-width: 224px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.moreDiv {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, Tooltip } from 'antd';
import type { ItemType } from 'antd/es/menu/interface';
import React from 'react';

import type { ModalState } from '../RoleModal/RoleModal';
import { RoleItem } from './RoleItem';
import styles from './RoleList.less';

export interface RoleListProps {
  roleList: {
    id: number;
    name: string;
    items: ItemType[];
  }[];
  onPress: (id: number) => void;
  title: string;
  button: {
    title: string;
    onPress: () => void;
    icon?: React.ReactNode;
    disabled?: boolean;
    tooltip?: string;
  };
  actionKey: number;
  onOpenModal: (
    state: ModalState,
    data?: {
      id: number;
      name?: string;
    },
  ) => void;
  initRoleList: () => void;
  onDeleteRole: (id: number) => void;
}

export const RoleList = (props: RoleListProps) => {
  const { roleList, title, button, actionKey, onPress, onOpenModal, onDeleteRole } = props;
  return (
    <div className={styles.roleList}>
      <div className={styles.titleDiv}>{title}</div>
      <div className={styles.listDiv}>
        {roleList.map((item) => (
          <RoleItem
            key={item.id}
            actionKey={actionKey}
            id={item.id}
            items={item.items}
            name={item.name}
            onDeleteRole={onDeleteRole}
            onOpenModal={onOpenModal}
            onPress={onPress}
          />
        ))}
      </div>
      <div className={styles.buttonDiv}>
        <Tooltip title={button.tooltip}>
          <Button
            block
            className={styles.styledButton}
            icon={button.icon ?? <PlusCircleOutlined />}
            onClick={button.onPress}
          >
            {button.title}
          </Button>
        </Tooltip>
      </div>
    </div>
  );
};

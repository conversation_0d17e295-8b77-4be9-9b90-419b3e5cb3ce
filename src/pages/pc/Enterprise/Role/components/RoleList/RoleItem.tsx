import { Dropdown, Tooltip } from 'antd';
import type { ItemType } from 'antd/es/menu/interface';
import React, { memo, useCallback, useMemo } from 'react';

import { ModalState } from '../RoleModal/RoleModal';
import styles from './RoleItem.less';

export enum MenuKey {
  Preview = 'preview',
  Edit = 'edit',
  Divider = 'divider',
  Delete = 'delete',
}

interface RoleItemProps {
  id: number;
  name: string;
  actionKey: number;
  items: ItemType[];
  onPress: (id: number) => void;
  onDeleteRole: (id: number) => void;
  onOpenModal: (
    state: ModalState,
    data?: {
      id: number;
      name?: string;
    },
  ) => void;
}

export const RoleItem = memo((props: RoleItemProps) => {
  const { id, name, actionKey, onPress, onDeleteRole, onOpenModal, items } = props;
  const action = useMemo(() => id === actionKey, [actionKey]);

  const onClick = useCallback(() => {
    onPress(id);
  }, [onPress, id]);

  const handleMenuClick = useCallback(
    async (e: { key: string }) => {
      switch (e.key) {
        case MenuKey.Delete:
          onDeleteRole(id);
          break;
        case MenuKey.Edit:
          onOpenModal(ModalState.Edit, {
            id,
            name,
          });
          break;
        case MenuKey.Preview:
          onOpenModal(ModalState.Preview, {
            id,
            name,
          });
          break;
        default:
          break;
      }
    },
    [id, onOpenModal, name, onDeleteRole],
  );

  // 阻止点击事件
  const handleStopPropagation = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
  };

  return (
    <div className={`${styles.roleItem} ${action ? styles.action : ''}`} onClick={onClick}>
      <Tooltip title={name}>
        <div className={styles.nameDiv}>{name}</div>
      </Tooltip>
      <div onClick={handleStopPropagation}>
        <Dropdown
          getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
          menu={{ items, onClick: handleMenuClick }}
          placement="bottomRight"
          trigger={['click']}
        >
          <div className={styles.moreDiv}>…</div>
        </Dropdown>
      </div>
    </div>
  );
});

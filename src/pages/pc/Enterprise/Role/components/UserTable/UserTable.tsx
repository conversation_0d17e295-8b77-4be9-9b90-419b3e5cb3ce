import { Button, Empty, Table, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { Key } from 'react';
import React, { useCallback } from 'react';

import emptyPng from '@/assets/role/empty.png';
import noShowPng from '@/assets/role/no-show.png';
import { fm2 } from '@/modules/Locale';

import { CREATOR_ROLE_ID, WHOLE_MEMBER_ROLE_ID } from '../../service/type';
import styles from './UserTable.less';

export interface ButtonProps {
  id: string;
  text: string;
  onPress: (id: string) => void;
  type?: 'primary' | 'default';
  danger?: boolean;
  icon?: React.ReactNode;
  disabled?: boolean;
  tooltip?: string;
}

export interface UserItemProps {
  name: string;
  email: string;
  avatar: string;
  id: number;
}

export interface UserTableProps {
  title: string;
  buttons: ButtonProps[];
  expansionButtons: ButtonProps[];
  userList: UserItemProps[];
  columns: ColumnsType<UserItemProps>;
  actionKey: number;
  tableLoading: boolean;
  scrollY: number;
  onChangeSelectedRowKeys: (selectedRowKeys: number[]) => void;
  handleTableScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

export const UserTable = (props: UserTableProps) => {
  const {
    title,
    userList,
    buttons,
    expansionButtons,
    columns,
    actionKey,
    scrollY,
    tableLoading,
    onChangeSelectedRowKeys,
    handleTableScroll,
  } = props;

  const ButtonComponent = (props: ButtonProps) => {
    const { id, text, onPress, type, danger, disabled, tooltip, icon } = props;
    const onClick = useCallback(() => {
      onPress(id);
    }, [onPress, id]);

    if (disabled) {
      return (
        <Tooltip placement="top" title={tooltip}>
          <button className={styles.disabledButton} type="button">
            {icon} {text}
          </button>
        </Tooltip>
      );
    }

    return (
      <Tooltip placement="top" title={tooltip}>
        <Button danger={danger} disabled={disabled} icon={icon} type={type} onClick={onClick}>
          {text}
        </Button>
      </Tooltip>
    );
  };

  const onChange = useCallback(
    (list: Key[]) => {
      onChangeSelectedRowKeys(list.map((item) => Number(item)));
    },
    [onChangeSelectedRowKeys],
  );

  return (
    <div className={styles.userTable}>
      <div className={styles.header}>
        <div className={styles.title}>
          {title}
          <span className={styles.count}>{fm2('Role.totalPeople')}</span>
          {actionKey === WHOLE_MEMBER_ROLE_ID ? '0' : userList.length}
        </div>
      </div>
      <div className={styles.buttons}>
        <div className={styles.buttonList}>
          {/* 角色查看或者修改 */}
          {buttons.map((button) => (
            <ButtonComponent key={button.id} {...button} />
          ))}
        </div>
        <div className={styles.buttonExpansion}>
          {/* 批量删除 */}
          {expansionButtons.map((button) => (
            <ButtonComponent key={button.id} {...button} />
          ))}
        </div>
      </div>
      {actionKey === WHOLE_MEMBER_ROLE_ID ? (
        // imageStyle={{ height: 170 }}
        <Empty className={styles.styledEmpty} description={fm2('Role.listDefaultRoleTips')} image={noShowPng} />
      ) : userList.length > 0 ? (
        <Table
          bordered
          className={styles.styledTable}
          columns={columns as ColumnsType<any>}
          dataSource={userList}
          loading={tableLoading}
          pagination={false}
          rowKey="id"
          rowSelection={
            actionKey !== CREATOR_ROLE_ID
              ? {
                  type: 'checkbox',
                  columnWidth: 32,
                  onChange,
                }
              : undefined
          }
          scroll={{
            scrollToFirstRowOnChange: true,
            y: scrollY,
          }}
          onScroll={handleTableScroll}
        />
      ) : (
        // imageStyle={{ height: 170 }}
        <Empty
          className={styles.styledEmpty}
          description={fm2('Role.listNoUserTips')}
          image={emptyPng}
          style={{ height: scrollY - 34 }}
        />
      )}
    </div>
  );
};

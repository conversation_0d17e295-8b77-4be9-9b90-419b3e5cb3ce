.userTable {
  min-width: 0;
  flex: 1;
  background: var(--theme-layout-color-bg-white);
  border-radius: 0 8px 8px 0;
  border: 1px solid var(--theme-separator-color-lighter);
  border-left: none;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  padding: 16px 24px;
  box-sizing: border-box;
  align-items: flex-end;
}

.title {
  color: var(--theme-text-color-default);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.count {
  margin-left: 16px;
  font-size: 14px;
  line-height: 24px;
  color: var(--theme-text-color-secondary);
}

.disabledButton {
  display: flex;
  padding: 4px 15px;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-button-color-disabled);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 2%);
  color: var(--theme-text-color-disabled);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  cursor: not-allowed;

  & > img {
    margin-right: 4px;
  }
}

.buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 24px 16px;
  box-sizing: border-box;
}

.buttonList {
  display: flex;

  > button,
  div {
    margin-left: 4px;
  }

  > button:first-child {
    margin-left: 0;
  }
}

.buttonExpansion {
  display: flex;

  > button {
    margin-left: 4px;
  }

  > button:first-child {
    margin-left: 0;
  }
}

.styledTable {
  padding: 0 24px 16px;

  &.ant-table-wrapper .ant-table-thead > tr > th {
    background: var(--theme-layout-color-bg-white);
  }

  &.ant-table-wrapper .ant-table-tbody > tr > td {
    padding: 6px 16px;
  }

  &.ant-table-wrapper .ant-table-thead > tr > .ant-table-selection-column,
  &.ant-table-wrapper .ant-table-row > .ant-table-selection-column {
    padding: 0;
    text-align: right;
  }

  &.ant-table-wrapper
    .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-body
    > table
    > tbody
    > tr
    > .ant-table-selection-column,
  &.ant-table-wrapper
    .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-header
    > table
    > thead
    > tr
    > th {
    border-right: none;
  }

  &.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
    border-left: 0;
  }
}

.styledEmpty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.email {
  text-overflow: ellipsis;
  overflow: hidden;
}

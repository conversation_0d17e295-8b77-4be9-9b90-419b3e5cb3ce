import { Modal } from 'antd';
import React, { useCallback } from 'react';

import { UserOrgSelector } from '@/components/UserOrgTree';
import type { UserOrgSelectorProps } from '@/components/UserOrgTree/UserOrgSelector';
import { fm } from '@/modules/Locale';

interface BindModalModalProps {
  org: UserOrgSelectorProps;
  open: boolean;
  actionKey: number;
  initUserList: (id: number) => void;
  onClose: () => void;
  handleBindModal: (id: number, initUserList: (id: number) => void) => void;
}

export const BindModal = (props: BindModalModalProps) => {
  const { org, open, onClose, handleBindModal, actionKey, initUserList } = props;

  const handleOk = useCallback(() => {
    handleBindModal(actionKey, initUserList);
  }, [handleBindModal, actionKey, initUserList]);

  return (
    <Modal
      centered
      destroyOnClose
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: org.selected.list?.length === 0,
      }}
      okText={fm('Role.confirm')}
      open={open}
      title={fm('Role.bindUser')}
      width={618}
      onCancel={onClose}
      onOk={handleOk}
    >
      <UserOrgSelector {...org} />
    </Modal>
  );
};

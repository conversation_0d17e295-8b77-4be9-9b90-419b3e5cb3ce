import type { MessageInstance } from 'antd/es/message/interface';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import type { OrgItemType } from '@/components/OrgSelectCard/type';
import type { UserOrgSelectorProps } from '@/components/UserOrgTree/UserOrgSelector';
import { ZERO } from '@/configs/configs';
import { DepartmentsContext, DepartmentsDispatchContext } from '@/contexts/departments';
import { getExtensionRule, postExtensionRule, putExtensionRuleById } from '@/contexts/departments/service/api';
import type { ObjectType } from '@/contexts/departments/type';
import { type MembershipScope, SELECT_BASIC } from '@/contexts/departments/type';
import { useOrgSelect } from '@/hooks/useOrgSelect';
import { fm2 } from '@/modules/Locale';
import { formatterSelectedList } from '@/utils/orgSelect';

import styles from '../departments.less';
import {
  convertMembershipScopeToOrg,
  convertOrgToMembershipScope,
  convertOwnersToMembershipScope,
  convertOwnersToOrg,
  convertTargetsToMembershipScope,
  formatterOpenSelectedList,
  uniqueByField,
} from '../utils';

/**
 * 最大选择对象或者范围
 */
const MAX_SELECTED_NUMBER = 10;

/**
 * 最大规则条数
 */
export const MAX_RULE_NUMBER = 100;

/**
 * 不带副标题的穿梭框高度
 */
export const HEIGHT_WITHOUT_SUBTITLE = 480;

/**
 * 带副标题的穿梭框高度
 */
export const HEIGHT_WITH_SUBTITLE = 420;

export const useExtendedRuleModal = ({ messageApi }: { messageApi: MessageInstance }) => {
  const { extendedRules } = useContext(DepartmentsContext);
  const dispatch = useContext(DepartmentsDispatchContext);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [openList, setOpenList] = useState<MembershipScope[]>([]);
  const [isNext, setIsNext] = useState<boolean>(false);
  const [isRuleVisibility, setIsRuleVisibility] = useState<boolean>(true);
  const [owners, setOwners] = useState<MembershipScope[]>([]);
  const [targets, setTargets] = useState<MembershipScope[]>([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [ruleId, setRuleId] = useState<number>();
  /**
   * FIXME: 不合理的需求：请求到的用户信息列表，用于编辑情况下获取用户邮箱
   */
  const [userList, setUserList] = useState<MembershipScope[]>([]);

  const {
    loading,
    crumbs,
    orgList,
    selectedList,
    orgSelectAll,
    initSelect,
    clearAllSelected,
    onScroll,
    clearOrgList,
    onSearch,
    clearSearch,
    searchResult,
    searchLoading,
  } = useOrgSelect({
    pageSize: 50,
    admin: true,
    enableSelectAllEnterprise: true,
  });

  const onChangeIsRuleVisibility = useCallback((visible: boolean) => {
    setIsRuleVisibility(visible);
  }, []);

  const onLastStep = useCallback(() => {
    setTargets(openList);
    setIsNext(false);
    clearAllSelected();
    clearOrgList();
    clearSearch();

    const initList: OrgItemType[] = convertMembershipScopeToOrg(owners, userList);
    initSelect(initList);
  }, [owners, userList, clearAllSelected, clearOrgList, clearSearch]);

  const onNext = useCallback(() => {
    setOwners(openList);
    clearAllSelected();
    clearOrgList();
    clearSearch();

    let initList: OrgItemType[] = [];

    if (targets) {
      initList = convertMembershipScopeToOrg(targets, userList);
    }

    initSelect(initList);
    setIsNext(true);
  }, [openList, targets, userList, initSelect, clearAllSelected, clearOrgList, clearSearch]);

  const getExtensionRuleList = useCallback(() => {
    dispatch?.({
      type: 'setDataItem',
      data: { tableLoading: true },
    });
    getExtensionRule()
      .then((rules) => {
        dispatch?.({
          type: 'setDataItem',
          data: {
            extendedRules: rules?.sort((a, b) => b.id - a.id) ?? [],
            tableLoading: false,
          },
        });
      })
      .catch(() => {
        messageApi.error(fm2('Organization.getDataFailedPleaseRetry'));

        dispatch?.({
          type: 'setDataItem',
          data: { tableLoading: false },
        });
      });
  }, [messageApi]);

  const onOpenExtensionRuleModal = useCallback(() => {
    if (extendedRules.length >= MAX_RULE_NUMBER) {
      messageApi.error(fm2('DndZone.maxRoule', { max: MAX_RULE_NUMBER }));
    } else {
      initSelect([]);
      setModalOpen(true);
    }
  }, [initSelect, extendedRules, messageApi]);

  const onEditRuleModal = useCallback(
    (id: number) => {
      // 编辑时写入owners和targets和visible
      let initList: OrgItemType[] = [];
      const currentRule = extendedRules.find((rule) => rule.id === id);
      if (currentRule) {
        initList = convertOwnersToOrg(currentRule.owners, userList);
        setIsRuleVisibility(currentRule.visible);
        setOwners(convertOwnersToMembershipScope(currentRule.owners, userList));
        setTargets(convertTargetsToMembershipScope(currentRule.targets, userList));
      }

      initSelect(initList);
      setIsEdit(true);
      setModalOpen(true);
      setRuleId(id);
    },
    [initSelect, extendedRules, userList],
  );

  const onResetModal = useCallback(() => {
    setIsNext(false);
    setIsEdit(false);
    setIsRuleVisibility(true);
    setOwners([]);
    setTargets([]);
    setOpenList([]);
    clearOrgList();
    clearSearch();
    clearAllSelected();
  }, [clearOrgList, clearSearch, clearAllSelected]);

  const onCancelExtensionRuleModal = useCallback(() => {
    setModalOpen(false);
    onResetModal();
  }, [onResetModal]);

  const org: UserOrgSelectorProps = useMemo(
    () => ({
      height: isNext ? HEIGHT_WITH_SUBTITLE : HEIGHT_WITHOUT_SUBTITLE,
      select: {
        loading: loading || searchLoading,
        onSearch,
        title: isNext ? fm2('Organization.searchOrSelectScope') : fm2('Organization.searchOrSelectObject'),
        placeholder: fm2('Organization.search'),
        selectAll: orgSelectAll,
        breadcrumb: crumbs
          ? {
              crumbs,
            }
          : undefined,
        search: searchResult
          ? {
              ...SELECT_BASIC,
              data: searchResult,
            }
          : undefined,
        org: {
          ...SELECT_BASIC,
          onScroll,
          data: orgList,
        },
      },
      selected: {
        header: {
          title: (
            <>
              <div className={styles.overflowEllipsisDiv}>{fm2('Organization.selected')}</div>
              <>({selectedList?.length ?? ZERO})</>
            </>
          ),
          onPress: clearAllSelected,
          pressText: fm2('Organization.clear'),
          disabled: selectedList?.length === ZERO || loading,
        },
        list: selectedList,
        itemRender: formatterOpenSelectedList,
      },
    }),
    [isNext, loading, searchLoading, orgSelectAll, crumbs, searchResult, onScroll, orgList, selectedList],
  );

  const updateOpenList = useCallback((newSelectedList: OrgItemType[]) => {
    setOpenList((list) =>
      formatterSelectedList<MembershipScope>({
        list,
        selectedList: newSelectedList,
        convertSelected: convertOrgToMembershipScope,
      }),
    );
  }, []);

  const onSubmit = useCallback(() => {
    if (isEdit && ruleId) {
      putExtensionRuleById(ruleId, {
        visible: isRuleVisibility,
        owners,
        targets: openList,
      })
        .then(() => {
          onCancelExtensionRuleModal();
          messageApi.success(fm2('Organization.operationSuccess'));
          getExtensionRuleList();
        })
        .catch(() => {
          messageApi.error(fm2('Organization.operationFailedRety'));
        });
    } else {
      postExtensionRule({
        visible: isRuleVisibility,
        owners,
        targets: openList,
      })
        .then(() => {
          onCancelExtensionRuleModal();
          messageApi.success(fm2('Organization.operationSuccess'));
          getExtensionRuleList();
        })
        .catch((e) => {
          if (e?.error?.code === 'STATUS_FORBIDDEN') {
            messageApi.error(fm2('Organization.ruleLimit'));
          } else {
            messageApi.error(fm2('Organization.operationFailedRety'));
          }
        });
    }
  }, [
    isEdit,
    ruleId,
    isRuleVisibility,
    owners,
    openList,
    messageApi,
    getExtensionRuleList,
    onCancelExtensionRuleModal,
  ]);

  useEffect(() => {
    updateOpenList(selectedList);
  }, [selectedList, updateOpenList]);

  useEffect(() => {
    setUserList((preUserList) =>
      uniqueByField<MembershipScope>(
        preUserList.concat(
          orgList.map((org) => ({
            ...org,
            name: org.title,
            type: org.type as unknown as ObjectType,
          })),
        ),
        'id',
      ),
    );
  }, [orgList]);

  const submit = useMemo(() => {
    return {
      text: isNext ? fm2('Organization.apply') : fm2('Organization.next'),
      disabled: openList?.length === ZERO || openList?.length > MAX_SELECTED_NUMBER,
      onSubmit: isNext ? onSubmit : onNext,
    };
  }, [onSubmit, onNext, isNext, openList]);

  useEffect(() => {
    if (openList?.length > MAX_SELECTED_NUMBER) {
      messageApi.open({
        key: 'max-warning',
        type: 'warning',
        content:
          fm2('Organization.maxSelect', { count: MAX_SELECTED_NUMBER }) + isNext
            ? fm2('Organization.applyObject')
            : fm2('Organization.range'),
        duration: 2,
      });
    }
  }, [openList?.length, isNext]);

  return {
    org,
    isNext,
    modalOpen,
    isRuleVisibility,
    submit,
    onChangeIsRuleVisibility,
    onSubmit,
    onLastStep,
    onOpenExtensionRuleModal,
    onEditRuleModal,
    onCancelExtensionRuleModal,
  };
};

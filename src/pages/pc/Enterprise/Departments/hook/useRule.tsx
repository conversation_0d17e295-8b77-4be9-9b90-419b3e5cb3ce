import type { TableColumnsType } from 'antd';
import { Popover, Select, Switch } from 'antd';
import type { MessageInstance } from 'antd/es/message/interface';
import { throttle } from 'lodash';
import React, { useCallback, useContext, useMemo, useState } from 'react';

import { DepartmentsContext, DepartmentsDispatchContext } from '@/contexts/departments';
import { deleteExtensionRule, putBaseRules, putExtensionRule } from '@/contexts/departments/service/api';
import type { BaseRules, ExtendedRule } from '@/contexts/departments/type';
import { RulePermission } from '@/contexts/departments/type';
import { fm2 } from '@/modules/Locale';
import { hasProperty } from '@/utils/typeScript';

import styles from '../components/ExtensionRule/ExtensionRule.less';
import { MemberTooltipCard } from '../components/ExtensionRule/MemberTooltipCard';
import { convertOwnersToMemberInfo, convertTargetsToMemberInfo } from '../utils';

interface RuleProps {
  messageApi: MessageInstance;
  onEditRuleModal: (id: number) => void;
}

// 一万
const MAX_TOOLTIP_WIDTH = 10000;

export const useRule = (props: RuleProps) => {
  const { messageApi, onEditRuleModal } = props;
  const { baseRules, extendedRules } = useContext(DepartmentsContext);
  const dispatch = useContext(DepartmentsDispatchContext);
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);
  const [ruleId, setRuleId] = useState<number>();

  const onChangeBaseRule = useCallback(
    (permission: Partial<BaseRules>) => {
      const newRule = {
        ...baseRules,
        ...permission,
      };
      if (newRule['permission'] !== RulePermission.department) {
        newRule.showSubDepartments = true;
      }

      putBaseRules(newRule)
        .then(() => {
          dispatch?.({
            type: 'setDataItem',
            data: { baseRules: newRule },
          });
        })
        .catch(() => {
          messageApi.error('error');
        });
    },
    [baseRules, messageApi],
  );

  const onPutExtensionRule = useCallback(
    (
      id: number,
      params: {
        enable?: boolean;
        visible?: boolean;
      },
    ) => {
      putExtensionRule(id, params)
        .then(() => {
          dispatch?.({
            type: 'PatchRule',
            data: { id: id, params },
          });

          messageApi.success(fm2('Organization.operationSuccess'));
        })
        .catch(() => {
          messageApi.error(fm2('Organization.operationFailed'));
        });
    },
    [messageApi],
  );

  const onOpenDeleteModal = useCallback((id: number) => {
    setRuleId(id);
    setDeleteModalOpen(true);
  }, []);

  const onCloseDeleteModal = useCallback(() => {
    setRuleId(undefined);
    setDeleteModalOpen(false);
  }, []);

  const onDeleteExtensionRule = useCallback(
    (id: number) => {
      deleteExtensionRule(id)
        .then(() => {
          dispatch?.({
            type: 'deleteRule',
            id,
          });
          messageApi.success(fm2('Organization.deleteSuccess'));
          onCloseDeleteModal();
          const newRules = extendedRules.filter((item) => item.id !== id);

          dispatch?.({
            type: 'setDataItem',
            data: { extendedRules: newRules ?? [], tableLoading: false },
          });
        })
        .catch((e) => {
          if (hasProperty(e, 'status')) {
            if (e.status === 401 || e.status === 403) {
              messageApi.error(fm2('Organization.noPermission'));
            } else if (e.status === 404) {
              messageApi.error(fm2('Organization.ruleNotExist'));
            } else if (e.status === 500) {
              messageApi.error(fm2('Organization.deleteFailed'));
            } else {
              messageApi.error(fm2('Organization.deleteFailed'));
            }
          } else {
            messageApi.error(fm2('Organization.deleteFailed'));
          }
        });
    },
    [messageApi, onCloseDeleteModal, extendedRules],
  );

  const onDeleteRule = useCallback(
    throttle(() => {
      if (ruleId) {
        onDeleteExtensionRule(ruleId);
      }
    }, MAX_TOOLTIP_WIDTH),
    [ruleId, onDeleteExtensionRule],
  );

  const onChangeRuleVisibleAndEnable = useCallback(
    (id: number, extend: { visible: boolean; enable: boolean }) => {
      onPutExtensionRule(id, extend);
    },
    [onPutExtensionRule],
  );

  const columns: TableColumnsType<ExtendedRule> = useMemo(
    () => [
      {
        key: 'name',
        dataIndex: 'owners',
        title: fm2('Organization.applicableObject'),
        ellipsis: true,
        render: (owners: ExtendedRule['owners']) => {
          if (owners?.length === 0) {
            return '-';
          }
          return (
            <Popover
              arrow={false}
              overlayInnerStyle={{
                padding: 0,
              }}
              placement="bottomLeft"
              title={<MemberTooltipCard list={convertOwnersToMemberInfo(owners)} />}
            >
              <div className={styles.tableColumnContainer}>
                <div className={styles.nameText}>{owners[0].ownerName}</div>
                {owners?.length > 1 ? fm2('Organization.etcOwners', { count: owners.length }) : null}
              </div>
            </Popover>
          );
        },
      },
      {
        key: 'rule',
        dataIndex: 'visible',
        title: fm2('Organization.rule'),
        width: 140,
        render: (visible: boolean, record: ExtendedRule) => {
          const onChangeVisible = (visible: boolean) => {
            onChangeRuleVisibleAndEnable(record.id, {
              visible,
              enable: record.enable,
            });
          };
          return (
            <Select
              options={[
                { value: true, label: fm2('Organization.visible') },
                { value: false, label: fm2('Organization.invisible') },
              ]}
              popupMatchSelectWidth={false}
              style={{ width: 108 }}
              value={visible}
              onChange={onChangeVisible}
            />
          );
        },
      },
      {
        key: 'scope',
        dataIndex: 'targets',
        title: fm2('Organization.scope'),
        ellipsis: true,
        render: (targets: ExtendedRule['targets']) => {
          if (targets.length === 0) {
            return '-';
          }
          return (
            <Popover
              arrow={false}
              overlayInnerStyle={{
                padding: 0,
              }}
              placement="bottomLeft"
              title={<MemberTooltipCard list={convertTargetsToMemberInfo(targets)} />}
            >
              <div className={styles.tableColumnContainer}>
                <div className={styles.nameText}>{targets[0].targetName}</div>
                {targets?.length > 1 ? fm2('Organization.etcTargets', { count: targets.length }) : null}
              </div>
            </Popover>
          );
        },
      },
      {
        key: 'operation',
        dataIndex: 'operation',
        title: fm2('Organization.operation'),
        width: 140,
        align: 'center',
        render: (_, record: ExtendedRule) => {
          const onDelete = () => {
            onOpenDeleteModal(record.id);
          };
          const onEdit = () => {
            onEditRuleModal(record.id);
          };
          return (
            <div className={styles.buttonGroup}>
              <div onClick={onEdit}>{fm2('Organization.edit')}</div>
              <div onClick={onDelete}>{fm2('Organization.delete')}</div>
            </div>
          );
        },
      },
      {
        key: 'enable',
        dataIndex: 'enable',
        title: fm2('Organization.effective'),
        width: 120,
        align: 'center',
        render: (enable: boolean, record: ExtendedRule) => {
          const onChangeEnable = throttle((enable: boolean) => {
            onChangeRuleVisibleAndEnable(record.id, {
              visible: record.visible,
              enable,
            });
          }, MAX_TOOLTIP_WIDTH); // 这里的接口在返回后会导致依赖变化，会重新render，render后又可以点击了

          return <Switch checked={enable} onChange={onChangeEnable} />;
        },
      },
    ],
    [onOpenDeleteModal, onEditRuleModal, onChangeRuleVisibleAndEnable],
  );

  return {
    ruleId,
    columns,
    deleteModalOpen,
    onDeleteRule,
    onChangeBaseRule,
    onOpenDeleteModal,
    onPutExtensionRule,
    onCloseDeleteModal,
    onDeleteExtensionRule,
  };
};

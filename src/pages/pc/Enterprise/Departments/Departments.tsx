import { message } from 'antd';
import React, { useContext } from 'react';

import { DepartmentsContext } from '@/contexts/departments';
import { useDocumentTitle } from '@/hooks/useDocumentTitle';
import { fm2 } from '@/modules/Locale';

import { BasicRule } from './components/BasicRule';
import { ExtensionRule } from './components/ExtensionRule';
import styles from './departments.less';
import { useExtendedRuleModal } from './hook/useExtendedRuleModal';
import { useRule } from './hook/useRule';

const { useMessage } = message;

export const Departments = () => {
  useDocumentTitle(fm2('Organization.organizationPermission'));
  const { baseRules, extendedRules } = useContext(DepartmentsContext);
  const [messageApi, contextHolder] = useMessage();
  const { onEditRuleModal, ...ruleModalProps } = useExtendedRuleModal({
    messageApi,
  });
  const { onChangeBaseRule, ...ruleProps } = useRule({
    messageApi,
    onEditRuleModal,
  });

  return (
    <div className={styles.departmentsController}>
      <div className={styles.DepartmentsContent}>
        <BasicRule {...baseRules} onChangeBaseRule={onChangeBaseRule} />
        <ExtensionRule
          applyToSearch={baseRules.applyToSearch}
          list={extendedRules}
          onChangeBaseRule={onChangeBaseRule}
          {...ruleProps}
          {...ruleModalProps}
        />
      </div>
      {contextHolder}
    </div>
  );
};

.departmentsController {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.DepartmentsContent {
  display: flex;
  flex-direction: column;
}

.overflowEllipsisDiv {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selectedList {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 4px 12px;
  border-block-end: unset;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: unset;
  }

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.userAvatar {
  width: 24px;
  height: 24px;
  flex: 0 0 24px;
  margin-right: 8px;
}

.selectedDepartmentIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 24px;
  min-width: 0;
  margin-right: 8px;

  & svg {
    width: 24px;
    height: 24px;
  }
}

.protectedSelectedContent {
  box-sizing: border-box;
  flex: 1;
  min-width: 0;
  margin-right: 8px;
  font-size: 13px;
  cursor: default;
  line-height: 20px;
}

.protectedSelectedTitle {
  box-sizing: border-box;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theme-text-color-default);
}

.protectedSelectedSubTitle {
  box-sizing: border-box;
  width: 100%;
  min-height: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--theme-text-color-secondary);
}

.clear {
  flex: 0 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

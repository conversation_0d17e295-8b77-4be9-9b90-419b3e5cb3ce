.basicRule {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-layout-color-bg-white);
}

.ruleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ruleTitle {
  color: var(--theme-text-color-default);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  align-items: center;
}

.ruleHeaderExtension {
  position: relative;

  .ant-select-single.ant-select-open .ant-select-selection-item {
    color: var(--theme-text-color-default);
  }

  .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color: var(--theme-text-color-default);
  }
}

.ruleContent {
  margin-top: 16px;
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-layout-color-bg-new-page);

  .ant-checkbox-wrapper {
    color: var(--theme-text-color-default);
  }
}

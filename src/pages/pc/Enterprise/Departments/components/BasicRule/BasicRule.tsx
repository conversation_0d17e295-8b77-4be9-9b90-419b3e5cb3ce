import { Checkbox, Select, Tooltip } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { useCallback } from 'react';

import { ReactComponent as StyledInfoSvg } from '@/assets/images/svg/info.svg';
import type { BaseRules } from '@/contexts/departments/type';
import { RulePermission } from '@/contexts/departments/type';
import { fm } from '@/modules/Locale';

import { BASE_PERMISSION_VISIBILITY } from '../../../constant';
import styles from './BasicRule.less';

interface BaseRulesProps extends Omit<BaseRules, 'applyToSearch'> {
  onChangeBaseRule: (permission: Partial<BaseRules>) => void;
}

export const BasicRule = (props: BaseRulesProps) => {
  const { permission, showMemberCount, showSubDepartments, onChangeBaseRule } = props;

  const onChangePermission = useCallback(
    (permission: RulePermission) => {
      onChangeBaseRule({ permission });
    },
    [onChangeBaseRule],
  );

  const onChangeSubDepartments = useCallback(
    (e: CheckboxChangeEvent) => {
      onChangeBaseRule({ showSubDepartments: e.target.checked });
    },
    [onChangeBaseRule],
  );

  const onChangeMemberCount = useCallback(
    (e: CheckboxChangeEvent) => {
      onChangeBaseRule({ showMemberCount: e.target.checked });
    },
    [onChangeBaseRule],
  );

  return (
    <div className={styles.basicRule}>
      <div className={styles.ruleHeader}>
        <div className={styles.ruleTitle}>
          {fm('Organization.basicRule')}
          <Tooltip
            // overlayStyle={{ maxWidth: MAX_TOOLTIP_WIDTH }}
            placement="right"
            title={fm('Organization.basicRuleTip')}
          >
            <StyledInfoSvg />
          </Tooltip>
        </div>
        <div className={styles.ruleHeaderExtension}>
          <Select
            dropdownStyle={{ right: 0, left: 'unset' }}
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
            options={BASE_PERMISSION_VISIBILITY}
            popupMatchSelectWidth={false}
            value={permission}
            onChange={onChangePermission}
          />
        </div>
      </div>
      <div className={styles.ruleContent}>
        <Checkbox
          checked={showSubDepartments}
          disabled={permission !== RulePermission.department}
          style={{ marginBottom: 16 }}
          onChange={onChangeSubDepartments}
        >
          {fm('Organization.basicRuleTip2')}
        </Checkbox>
        <Checkbox checked={showMemberCount} onChange={onChangeMemberCount}>
          {fm('Organization.showDepartmentMember')}
        </Checkbox>
      </div>
    </div>
  );
};

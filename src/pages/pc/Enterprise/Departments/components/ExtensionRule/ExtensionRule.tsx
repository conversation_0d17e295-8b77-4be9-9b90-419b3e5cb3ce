import type { TableColumnsType } from 'antd';
import { Button, Checkbox, Empty, Table, Tooltip } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { useCallback, useContext, useEffect, useState } from 'react';

import emptyPng from '@/assets/components/empty/<EMAIL>';
import { ReactComponent as StyledInfoSvg } from '@/assets/images/svg/info.svg';
import type { UserOrgSelectorProps } from '@/components/UserOrgTree/UserOrgSelector';
import { DepartmentsContext } from '@/contexts/departments';
import type { BaseRules, ExtendedRule } from '@/contexts/departments/type';
import { fm2 } from '@/modules/Locale';
import { concatImageUrl } from '@/utils/image';

import { DeleteModal } from './DeleteModal';
import styles from './ExtensionRule.less';
import { ExtensionRuleModal } from './ExtensionRuleModal';

interface ExtensionRuleProps {
  list: ExtendedRule[];
  applyToSearch: boolean;
  deleteModalOpen: boolean;
  modalOpen: boolean;
  isNext: boolean;
  ruleId?: number;
  columns: TableColumnsType<ExtendedRule>;
  org: UserOrgSelectorProps;
  isRuleVisibility: boolean;
  onChangeIsRuleVisibility: (visible: boolean) => void;
  onOpenExtensionRuleModal: () => void;
  onCancelExtensionRuleModal: () => void;
  onDeleteRule: () => void;
  submit: {
    onSubmit: () => void;
    text: string;
    disabled: boolean;
  };
  onCloseDeleteModal: () => void;
  onLastStep: () => void;
  onChangeBaseRule: (permission: Partial<BaseRules>) => void;
}

/**
 * table以外的高度
 */
const RESIDUAL_HEIGHT = 554;

export const ExtensionRule = (props: ExtensionRuleProps) => {
  const {
    list,
    applyToSearch,
    deleteModalOpen,
    modalOpen,
    columns,
    org,
    isNext,
    isRuleVisibility,
    onChangeIsRuleVisibility,
    onLastStep,
    onOpenExtensionRuleModal,
    onCancelExtensionRuleModal,
    onDeleteRule,
    onCloseDeleteModal,
    onChangeBaseRule,
    submit,
  } = props;
  const { tableLoading } = useContext(DepartmentsContext);
  const [scrollY, setScrollY] = useState<number>(window.innerHeight - RESIDUAL_HEIGHT);

  // table动态高度
  useEffect(() => {
    const handleResize = () => {
      setScrollY(Math.max(window.innerHeight - RESIDUAL_HEIGHT));
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const onChangeApplyToSearch = useCallback(
    (e: CheckboxChangeEvent) => {
      onChangeBaseRule({ applyToSearch: e.target.checked });
    },
    [onChangeBaseRule],
  );

  const emptyText = () => {
    return (
      <Empty
        className={styles.emptyContainer}
        description={<div className={styles.emptyText}>{fm2('Organization.noRule')}</div>}
        image={concatImageUrl(emptyPng)}
        style={{ height: scrollY - 25 }}
      />
    );
  };

  return (
    <div className={styles.extensionTule}>
      <div className={styles.ruleHeader}>
        <div className={styles.ruleTitle}>
          {fm2('Organization.supplementRule')}
          <Tooltip placement="right" title={fm2('Organization.supplementRuleTip')}>
            <StyledInfoSvg />
          </Tooltip>
        </div>
        <div className={styles.ruleHeaderExtension}>
          <Button onClick={onOpenExtensionRuleModal}>{fm2('Organization.addSupplementRule')}</Button>
        </div>
      </div>
      <div className={styles.ruleContent} style={{ height: scrollY + 53 }}>
        <Table
          bordered
          className={styles.styledTable}
          columns={columns as TableColumnsType<unknown>}
          dataSource={list}
          loading={tableLoading}
          locale={{
            emptyText: emptyText,
          }}
          pagination={false}
          rowKey="id"
          scroll={{ y: scrollY }}
        />
      </div>
      <div className={styles.ruleFooter}>
        <Checkbox checked={applyToSearch} onChange={onChangeApplyToSearch}>
          {fm2('Organization.addSupplementRuleTip')}
        </Checkbox>
      </div>
      <DeleteModal modalOpen={deleteModalOpen} onCloseDeleteModal={onCloseDeleteModal} onDeleteRule={onDeleteRule} />
      <ExtensionRuleModal
        isNext={isNext}
        isRuleVisibility={isRuleVisibility}
        modalOpen={modalOpen}
        org={org}
        submit={submit}
        onCancel={onCancelExtensionRuleModal}
        onChangeIsRuleVisibility={onChangeIsRuleVisibility}
        onLastStep={onLastStep}
      />
    </div>
  );
};

import { Avatar } from 'antd';
import React from 'react';

import styles from './memberTooltipCard.less';

export const MemberTooltipCard = ({
  list,
}: {
  list: { name: string; avatar: string; id: number; icon?: React.ReactNode }[];
}) => {
  return (
    <div className={styles.memberTooltip}>
      {list.map((user) => {
        return (
          <div key={user.id} className={styles.member}>
            {user.avatar ? (
              <Avatar className={styles.memberAvatar} src={user.avatar} />
            ) : (
              <div className={styles.icon}>{user.icon}</div>
            )}
            <div className={styles.memberName}>{user.name}</div>
          </div>
        );
      })}
    </div>
  );
};

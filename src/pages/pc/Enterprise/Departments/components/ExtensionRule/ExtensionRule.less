.extensionTule {
  margin-top: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-layout-color-bg-white);
}

.ruleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ruleTitle {
  display: flex;
  align-items: center;
  color: var(--theme-text-color-default);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.ruleHeaderExtension {
  position: relative;
}

.ruleContent {
  margin-top: 16px;
  display: flex;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  position: relative;
  flex: 1;
  min-height: 54px;
  overflow: hidden;
}

.styledTable {
  position: absolute;
  width: 100%;

  &.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
    border-left: none;
  }

  &.ant-table-wrapper .ant-table-thead > tr > th {
    background-color: var(--theme-layout-color-bg-white);
    border-right: none !important;
    color: var(--theme-text-color-medium);
    font-size: 13px;
    font-weight: 500;
    line-height: 18px;
  }

  &.ant-table-wrapper .ant-table-body .ant-table-tbody > tr > td {
    line-height: 20px;
    padding: 6px 16px;
    color: var(--theme-text-color-default);
    font-size: 13px;
  }

  &.ant-table-wrapper .ant-table-tbody > .ant-table-placeholder > td {
    border-bottom: none;
  }

  &.ant-table-wrapper
    .ant-table.ant-table-bordered
    > .ant-table-container
    > .ant-table-body
    > table
    > tbody
    > tr
    > td:last-child {
    border-right: none;
  }

  .ant-select-single .ant-select-selector {
    color: var(--theme-text-color-default) !important;
  }
}

.ruleFooter {
  z-index: 1;
  background-color: var(--theme-layout-color-bg-white);
  position: relative;
  padding: 16px 0 0;

  .ant-checkbox-wrapper {
    color: var(--theme-text-color-default);
    font-size: 14px;
    line-height: 22px;
  }
}

.buttonGroup {
  display: flex;
  justify-content: center;
  cursor: pointer;

  :last-child {
    margin-left: 20px;
  }
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.emptyText {
  color: var(--theme-text-color-secondary);
}

.tableColumnContainer {
  width: 100%;
  display: flex;
  cursor: pointer;
}

.nameText {
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 4px;
}

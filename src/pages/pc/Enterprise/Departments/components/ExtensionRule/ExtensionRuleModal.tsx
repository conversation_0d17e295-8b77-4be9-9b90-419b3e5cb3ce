import type { RadioChangeEvent } from 'antd';
import { Checkbox, Modal } from 'antd';
import React, { useCallback } from 'react';

import { LinkButton } from '@/components/LinkButton';
import { UserOrgSelector } from '@/components/UserOrgTree';
import type { UserOrgSelectorProps } from '@/components/UserOrgTree/UserOrgSelector';
import { fm2 } from '@/modules/Locale';

import styles from './ExtensionRuleModal.less';

export const ExtensionRuleModal = (props: {
  isNext: boolean;
  modalOpen: boolean;
  org: UserOrgSelectorProps;
  isRuleVisibility: boolean;
  onChangeIsRuleVisibility: (visible: boolean) => void;
  onLastStep: () => void;
  onCancel: () => void;
  submit: {
    onSubmit: () => void;
    text: string;
    disabled: boolean;
  };
}) => {
  const { modalOpen, org, onCancel, submit, isNext, isRuleVisibility, onChangeIsRuleVisibility, onLastStep } = props;

  const onChangeRuleVisibility = useCallback(
    (e: RadioChangeEvent) => {
      if (e.target.checked) {
        onChangeIsRuleVisibility(true);
      }
    },
    [onChangeIsRuleVisibility],
  );
  const onChangeRuleInvisible = useCallback(
    (e: RadioChangeEvent) => {
      if (e.target.checked) {
        onChangeIsRuleVisibility(false);
      }
    },
    [onChangeIsRuleVisibility],
  );

  return (
    <Modal
      centered
      destroyOnClose
      className={styles.styledModal}
      footer={(_, { OkBtn, CancelBtn }) => {
        if (!isNext) {
          return (
            <>
              <OkBtn />
              <CancelBtn />
            </>
          );
        }
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <LinkButton onClick={onLastStep}>{fm2('Organization.previous')}</LinkButton>
            <div className={styles.buttonGroup}>
              <OkBtn />
              <CancelBtn />
            </div>
          </div>
        );
      }}
      height={618}
      maskClosable={false}
      okButtonProps={{
        disabled: submit.disabled,
      }}
      okText={submit.text}
      open={modalOpen}
      style={{ width: 618, height: 618 }}
      title={fm2('Organization.ruleSetting')}
      width={618}
      onCancel={onCancel}
      onOk={submit.onSubmit}
    >
      {isNext ? <div className={styles.subTitle}>{fm2('Organization.setRule')}</div> : null}
      {isNext ? (
        <div className={styles.checkboxGroup}>
          <Checkbox checked={isRuleVisibility} onChange={onChangeRuleVisibility}>
            {fm2('Organization.visible')}
          </Checkbox>
          <Checkbox checked={!isRuleVisibility} onChange={onChangeRuleInvisible}>
            {fm2('Organization.invisible')}
          </Checkbox>
        </div>
      ) : null}
      <div className={styles.subTitle}>
        {isNext ? fm2('Organization.setRange') : fm2('Organization.selectApplyObject')}
      </div>
      <UserOrgSelector {...org} />
    </Modal>
  );
};

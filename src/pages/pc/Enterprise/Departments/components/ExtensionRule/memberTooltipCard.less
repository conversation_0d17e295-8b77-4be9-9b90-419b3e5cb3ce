.memberTooltip {
  display: flex;
  padding: 8px 12px;
  box-sizing: border-box;
  flex-direction: column;
  justify-content: center;
  width: 300px;
  background-color: var(--theme-layout-color-bg-white);

  div:first-child {
    margin-top: 0;
  }
}

.member {
  margin-top: 4px;
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.memberAvatar {
  width: 24px;
  height: 24px;
  margin-right: 4px;
  border: unset;
  flex: 0 0 24px;
}

.icon {
  width: 24px;
  height: 24px;
}

.memberName {
  flex: 1;
  margin-left: 4px;
  color: var(--theme-text-color-default);
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  white-space: normal;
  word-break: break-word;
}

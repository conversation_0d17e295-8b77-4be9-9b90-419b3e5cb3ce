import React, { useContext } from 'react';

import { DepartmentsProvider } from '@/contexts/departments';
import { PermissionContext } from '@/contexts/permissions';

import { Departments } from './Departments';
import { useCheckPermission } from './hook/useCheckPermission';

const DepartmentsPage = () => {
  const permission = useContext(PermissionContext);
  const { isGuide, isEnabled } = useCheckPermission();

  if (!permission?.loaded) {
    return <span>...</span>;
  }

  if (isGuide) {
    return false;
  }

  if (!isEnabled) {
    window.location.href = '/enterprise/settings';
    return null;
  }

  return <Departments />;
};

export const DepartmentsWithContext = () => {
  return (
    <DepartmentsProvider>
      <DepartmentsPage />
    </DepartmentsProvider>
  );
};

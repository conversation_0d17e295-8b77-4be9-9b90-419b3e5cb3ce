import { Avatar } from 'antd';
import type { ReactNode } from 'react';
import React from 'react';

import { ReactComponent as StyledCancelIcon } from '@/assets/images/svg/cancel-icon.svg';
import type { OrgItemType } from '@/components/OrgSelectCard/type';
import { OrgType } from '@/components/OrgSelectCard/type';
import { type MembershipScope, ObjectType } from '@/contexts/departments/type';
import { getOrgIcon } from '@/utils/orgSelect';

import styles from './departments.less';

export const convertOrgToMembershipScope = (org: OrgItemType): MembershipScope => {
  const { id, type, title, avatar, email, icon, onCancel } = org;
  return {
    id,
    name: title,
    icon,
    avatar,
    email,
    type: type === OrgType.User ? ObjectType.User : ObjectType.Department,
    onCancel,
  };
};

export const convertOwnersToOrg = (
  owners: {
    ownerId: number;
    ownerName: string;
    ownerAvatar: string;
    ownerType: ObjectType;
  }[],
  userList: MembershipScope[],
): OrgItemType[] => {
  return owners.map((owner) => {
    const orgType = owner.ownerType === ObjectType.Department ? OrgType.Department : OrgType.User;
    const icon = getOrgIcon({
      id: owner.ownerId,
      title: owner.ownerName,
      type: orgType,
    });
    const email = userList.find((user) => user.id === owner.ownerId)?.email;

    return {
      ...owner,
      orgType,
      icon,
      id: owner.ownerId,
      name: owner.ownerName,
      title: owner.ownerName,
      email,
      type: orgType,
      avatar: owner.ownerAvatar,
      checked: true,
      checkable: true,
    };
  });
};

export const convertTargetsToMemberInfo = (
  targets: {
    targetId: number;
    targetName: string;
    targetAvatar: string;
    targetType: ObjectType;
  }[],
): {
  id: number;
  name: string;
  avatar: string;
  icon?: React.ReactNode;
}[] => {
  return targets.map((target) => {
    const orgType = target.targetType === ObjectType.Department ? OrgType.Department : OrgType.User;
    const icon = getOrgIcon({
      id: target.targetId,
      title: target.targetName,
      type: orgType,
    });
    return {
      id: target.targetId,
      name: target.targetName,
      title: target.targetName,
      avatar: target.targetAvatar,
      icon,
    };
  });
};

export const convertOwnersToMemberInfo = (
  owners: {
    ownerId: number;
    ownerName: string;
    ownerAvatar: string;
    ownerType: ObjectType;
  }[],
): {
  id: number;
  name: string;
  avatar: string;
  icon?: React.ReactNode;
}[] => {
  return owners.map((owner) => {
    const orgType = owner.ownerType === ObjectType.Department ? OrgType.Department : OrgType.User;
    const icon = getOrgIcon({
      id: owner.ownerId,
      title: owner.ownerName,
      type: orgType,
    });
    return {
      id: owner.ownerId,
      name: owner.ownerName,
      title: owner.ownerName,
      avatar: owner.ownerAvatar,
      icon,
    };
  });
};

export const convertOwnersToMembershipScope = (
  owners: {
    ownerId: number;
    ownerName: string;
    ownerAvatar: string;
    ownerType: ObjectType;
  }[],
  userList: MembershipScope[],
): MembershipScope[] => {
  return owners.map((owner) => {
    const email = userList.find((user) => user.id === owner.ownerId)?.email;
    return {
      id: owner.ownerId,
      name: owner.ownerName,
      type: owner.ownerType,
      avatar: owner.ownerAvatar,
      email,
      checked: true,
      checkable: true,
    };
  });
};

export const convertTargetsToMembershipScope = (
  targets: {
    targetId: number;
    targetName: string;
    targetAvatar: string;
    targetType: ObjectType;
  }[],
  userList: MembershipScope[],
): MembershipScope[] => {
  return targets.map((target) => {
    const email = userList.find((user) => user.id === target.targetId)?.email;
    return {
      id: target.targetId,
      name: target.targetName,
      type: target.targetType,
      email,
      avatar: target.targetAvatar,
      checked: true,
      checkable: true,
    };
  });
};

export const convertMembershipScopeToOrg = (
  membershipScope: MembershipScope[],
  userList: MembershipScope[],
): OrgItemType[] => {
  return membershipScope.map((membership) => {
    const orgType = membership.type === ObjectType.Department ? OrgType.Department : OrgType.User;
    const icon = getOrgIcon({
      id: membership.id,
      title: membership.name,
      type: orgType,
    });
    const email = userList.find((user) => user.id === membership.id)?.email;

    return {
      ...membership,
      orgType,
      icon,
      email,
      id: membership.id,
      name: membership.name,
      title: membership.name,
      type: orgType,
      avatar: membership.avatar,
      checked: true,
      checkable: true,
    };
  });
};

export const uniqueByField = <T extends Record<string, any>>(arr: T[], field: keyof T): T[] =>
  arr.reduce((acc, current) => {
    const x = acc.find((item) => item[field] === current[field]);
    if (!x) {
      return acc.concat([current]);
    } else {
      return acc;
    }
  }, [] as T[]);

export function formatterOpenSelectedList(selected: OrgItemType): ReactNode {
  const { id, title, icon, avatar, email, onCancel } = selected;
  return (
    <div key={id} className={styles.selectedList} title={title}>
      {avatar && (
        <Avatar className={styles.userAvatar} src={avatar}>
          {title}
        </Avatar>
      )}
      {icon && <div className={styles.selectedDepartmentIcon}>{icon}</div>}
      <div className={styles.protectedSelectedContent}>
        <div className={styles.protectedSelectedTitle}>{title}</div>
        {email && <div className={styles.protectedSelectedSubTitle}>{email}</div>}
      </div>
      <div className={styles.clear}>
        <StyledCancelIcon style={{ cursor: 'pointer' }} onClick={onCancel} />
      </div>
    </div>
  );
}

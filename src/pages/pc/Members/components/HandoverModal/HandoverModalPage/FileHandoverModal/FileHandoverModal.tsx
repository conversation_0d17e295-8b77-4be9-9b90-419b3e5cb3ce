import { Button, message, Modal, Tooltip } from 'antd';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { createSnapshot, getChildrenFiles } from '@/api/handover';
import { DEFAULT_GUID, HandoverContext, HandoverDispatchContext, HandoverProvider } from '@/contexts/handover';
import type { TreeFile } from '@/model/handover';
import { PageType } from '@/model/handover';
import { fm } from '@/modules/Locale';
import { copyHandle } from '@/pages/pc/Enterprise/utils';

import { Transferee } from '../../components/Transferee';
import { ConfirmationProcess } from './ConfirmationProcess';
import type { CrumbsList } from './Crumbs';
import { FileSelectionList } from './FileSelectionList';
import { HandoverTips } from './HandoverTips';
import styles from './index.less';

enum StepNumberType {
  // 选择文件
  SelectFile = 'select-file',
  // 确认交接文件
  ConfirmHandoverFile = 'confirm-handover-file',
  // 确认交接
  ConfirmHandover = 'confirm-handover',
}

interface FileHandoverModalProps {
  handoverId: number;
  isModal?: boolean;
  name?: string;
  demission?: boolean;
  avatar?: string;
  onCancel?: () => void;
  id?: string | number | undefined;
  onOK?: () => void;
}

enum MessageType {
  HandoverFilePicker = 'handoverFilePicker',
}

enum MessageOperation {
  CloseModal = 'onClose',
}

const FileHandoverModal = (props: FileHandoverModalProps) => {
  const { handoverId, isModal, onCancel: propsCancel, name, demission: propsDemission, avatar, id, onOK } = props;
  const [messageApi, contextHolder] = message.useMessage();
  const [stepNumber, setStepNumber] = useState<StepNumberType>(StepNumberType.SelectFile);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    okText: string;
    cancelText: string;
  }>();

  const s18nText = {
    allFiles: fm('hanover.allFiles'),
    handovered: fm('hanover.handovered'),
    previousStep: fm('hanover.previousStep'),
    selectFiles: fm('hanover.selectFiles'),
    fileSelect: fm('hanover.fileSelect'),
    nextStep: fm('hanover.nextStep'),
    cancel: fm('hanover.cancel'),
    confirmHandover: fm('hanover.confirmHandover'),
    handovering: fm('hanover.handovering'),
    copy: fm('hanover.copy'),
    close: fm('hanover.close'),
  };
  const handover = useContext(HandoverContext);
  const dispatch = useContext(HandoverDispatchContext);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [token, setToken] = useState<string>();

  const handoverName = name ?? new URLSearchParams(location.search).get('name') ?? '';
  const demission =
    propsDemission !== undefined ? propsDemission : new URLSearchParams(location.search).get('demission') === 'true';
  const handoverAvatar = avatar ?? new URLSearchParams(location.search).get('avatar') ?? '';
  const okButtonDisabled = selectedFiles.length === 0;

  const handoverUrl = `${location.origin}/organization/${demission ? 'handover' : 'handoff'}/${token}`;
  const copyContent = fm('hanover.handoverLink', { handoverName, handoverUrl });

  const getCrumbsList = useCallback(
    (guid: string, arr: CrumbsList) => {
      if (handover?.flattenFiles) {
        const parentFile = handover?.flattenFiles?.find((file) => file?.guid === guid);
        if (parentFile) {
          arr.unshift({
            id: parentFile.guid,
            name: parentFile.name,
          });
          if (parentFile.parentGuid) {
            getCrumbsList(parentFile?.parentGuid, arr);
          }
        }
      }
      return arr;
    },
    [handover?.flattenFiles],
  );

  const crumbsList: CrumbsList = useMemo(() => {
    const crumbs: CrumbsList = [];
    if (!handover?.currentFolder?.guid) {
      return [
        {
          id: DEFAULT_GUID,
          name: s18nText.allFiles,
        },
      ];
    }

    return getCrumbsList(handover?.currentFolder?.guid, crumbs);
  }, [handover?.currentFolder?.guid, getCrumbsList, s18nText.allFiles]);

  const onCrumbsClick = useCallback(
    (id: string) => {
      dispatch?.({
        type: 'backFolderGuid',
        guid: id,
      });

      // 将横向滚动条滚动到最右侧
      const scrollElement = document.querySelector('.sm-crumbs-file');
      if (scrollElement) {
        scrollElement.scrollLeft = scrollElement.scrollWidth - scrollElement.clientWidth;
      }
    },
    [dispatch],
  );

  // 当前文件列表里被选中的guid
  const currentFolderSelectedFiles: string[] = useMemo(() => {
    return selectedFiles
      .map((guid) => {
        if (handover?.files?.find((file) => file.guid === guid)) {
          return guid;
        } else {
          return undefined;
        }
      })
      .filter(Boolean) as string[];
  }, [selectedFiles, handover?.files]);

  const parentCheck = useMemo(() => {
    return crumbsList.map((file) => file.id).some((guid) => selectedFiles.includes(guid));
  }, [crumbsList, selectedFiles]);

  const isSubset = useCallback((arr1: string[], arr2: string[]): boolean => {
    return arr1.every((element) => arr2.includes(element));
  }, []);

  /**
   * 多选进位
   */
  const carry = useCallback(
    (preArr: string[]) => {
      let arr = [...preArr];

      // 所有父级文件
      const allParentFiles: TreeFile[] = [];
      crumbsList
        .filter((item) => item.id !== DEFAULT_GUID)
        .reverse()
        .forEach((crumbs) => {
          handover?.flattenFiles?.forEach((file) => {
            if (file?.guid === crumbs.id) {
              allParentFiles.push(file);
            }
          });
        });
      allParentFiles.forEach((file) => {
        const childrenGuids = file?.children?.map((item) => item.guid);

        if (childrenGuids && isSubset(childrenGuids, arr)) {
          arr = arr.filter((itemGuid) => !childrenGuids.includes(itemGuid));
          if (file?.guid) {
            arr.push(file?.guid);
          }
        }
      });
      return arr;
    },
    [isSubset, crumbsList, handover?.flattenFiles],
  );

  /**
   * 该guid是否是list的递归子文件
   */
  const isSubFile = useCallback((guid: string, list: TreeFile[]): boolean => {
    return list.some((item) => {
      if (item.guid === guid) {
        return true;
      } else if (item.children) {
        return isSubFile(guid, item.children);
      } else {
        return false;
      }
    });
  }, []);

  const onChangeSelectedFile = useCallback(
    (guid: string) => {
      // 自己在但祖先不在，取消自己的选择状态
      if (selectedFiles.includes(guid) && !parentCheck) {
        setSelectedFiles(selectedFiles.filter((file) => file !== guid));
      }
      // 自己在，祖先在，是一个错误状态，理论上要报错
      if (selectedFiles.includes(guid) && parentCheck) {
        // 报错
      }
      // 自己不在，祖先不在
      if (!selectedFiles.includes(guid) && !parentCheck) {
        let arr: string[] = [...selectedFiles];
        // 检测选中自己后是否选择父级所有子文件，是则取消所有子文件的选择状态并选中父级，并取消选中所有的子级文件
        if (
          currentFolderSelectedFiles.length === (handover?.files.length ?? 0) - 1 &&
          handover?.currentFolder.guid &&
          handover?.currentFolder.guid !== DEFAULT_GUID
        ) {
          const list = arr.filter((selectedGuid) => {
            const item = handover?.flattenFiles.find((file) => file.guid === selectedGuid);
            return (
              !currentFolderSelectedFiles.includes(selectedGuid) &&
              item?.parentGuid &&
              ![...arr, handover?.currentFolder.guid].includes(item?.parentGuid)
            );
          });
          arr = [...list, handover?.currentFolder.guid];
        } else {
          // ，否则只选中自己, 并取消选中所有的子级文件
          const list = arr.filter((selectedGuid) => {
            const item = handover?.flattenFiles.find((file) => file.guid === selectedGuid);
            return item?.parentGuid && ![...arr, guid].includes(item?.parentGuid);
          });
          arr = [...list, guid];
        }
        // 检察一下是否存在全选了所有子项的数据，将其所有子数据抹去选中父级
        setSelectedFiles(carry(arr));
      }
      // 自己不在祖先在，则取祖先的除了自己的子文件，并取消选中祖先
      if (!selectedFiles.includes(guid) && parentCheck) {
        // 所有父级文件
        const allParentFiles: TreeFile[] = [];
        crumbsList
          .filter((item) => item.id !== DEFAULT_GUID)
          .forEach((crumbs) => {
            handover?.flattenFiles?.forEach((file) => {
              if (file?.guid === crumbs.id) {
                allParentFiles.push(file);
              }
            });
          });

        let arr: string[] = [...selectedFiles];

        //  取父亲的除了自己的子文件，并取消选中父亲
        crumbsList
          .filter((item) => item.id !== DEFAULT_GUID)
          .map((crumbs) => crumbs.id)
          .forEach((crumbsGuid: string) => {
            if (arr.includes(crumbsGuid)) {
              const parentFiles = handover?.flattenFiles
                .find((file) => file?.guid === crumbsGuid)
                ?.children?.filter((item) => item?.guid !== guid)
                .map((item) => item?.guid);
              if (parentFiles) {
                const list = arr.filter((preGuid) => !parentFiles.includes(preGuid) && preGuid !== crumbsGuid);
                arr = [...list, ...parentFiles];
              }
            }
          });
        setSelectedFiles(carry(arr));
      }
    },
    [
      selectedFiles,
      parentCheck,
      currentFolderSelectedFiles,
      handover?.files.length,
      handover?.currentFolder.guid,
      handover?.flattenFiles,
      carry,
      crumbsList,
    ],
  );

  const onResetSelectedFiles = () => {
    setSelectedFiles([]);
  };

  const onSelectAll = useCallback(
    (flag: boolean) => {
      if (!handover?.currentFolder.guid) {
        return;
      }
      // 选中
      if (flag) {
        // 桌面
        if (handover.currentFolder.guid === DEFAULT_GUID) {
          // 全选桌面所有文件
          setSelectedFiles(handover?.files?.map((file) => file.guid));
        } else {
          // 取消当前文件的子集文件
          const list = selectedFiles.filter(
            (guid) => !currentFolderSelectedFiles.includes(guid) && !isSubFile(guid, handover?.files),
          );

          setSelectedFiles(carry([...list, handover.currentFolder.guid]));
        }
        // 取消选中
      } else {
        // 取消桌面所有文件
        if (handover.currentFolder.guid === DEFAULT_GUID) {
          setSelectedFiles([]);
        } else if (parentCheck) {
          // 如果父级存在则取消父级
          if (selectedFiles.includes(handover.currentFolder.guid)) {
            const list = selectedFiles.filter((guid) => guid !== handover.currentFolder.guid);
            setSelectedFiles(carry(list));
          } else {
            // 如果父级不存在则根据crumbsList往上找到存在选中的多倍祖父级，并取消选中这个祖父，并选中祖父不包含crumbsList里的子集
            let arr: string[] = [...selectedFiles];

            crumbsList
              .filter((item) => item.id !== DEFAULT_GUID)
              .map((crumbs) => crumbs.id)
              .forEach((crumbsGuid: string) => {
                if (arr.includes(crumbsGuid)) {
                  const parentFiles = handover.flattenFiles
                    .find((file) => file.guid === crumbsGuid)
                    ?.children?.filter((item) => item.guid !== handover.currentFolder.guid)
                    .map((item) => item.guid);
                  if (parentFiles) {
                    const list = arr.filter((preGuid) => !parentFiles.includes(preGuid) && preGuid !== crumbsGuid);
                    arr = [...list, ...parentFiles];
                  }
                }
              });
            setSelectedFiles(arr);
          }
        }
      }
    },
    [
      handover?.currentFolder.guid,
      handover?.files,
      handover?.flattenFiles,
      selectedFiles,
      carry,
      currentFolderSelectedFiles,
      isSubFile,
      parentCheck,
      crumbsList,
    ],
  );

  const onClose = (guid: string) => {
    setSelectedFiles(selectedFiles.filter((file) => file !== guid));
  };

  const resetStep = () => {
    setStepNumber(StepNumberType.SelectFile);
  };

  const onPress = useCallback(
    (guid: string) => {
      if (guid === DEFAULT_GUID) {
        dispatch?.({
          type: 'backFolderGuid',
          guid: guid,
        });
      } else {
        dispatch?.({
          type: 'setLoading',
          flag: true,
        });
        getChildrenFiles({
          guid,
          leftUser: handoverId,
          pageType: PageType.INITIATE_HANDOVER,
        })
          .then((files) => {
            dispatch?.({
              type: 'setFiles',
              files: files,
              guid: guid,
            });
          })
          .catch(undefined)
          .finally(() => {
            dispatch?.({
              type: 'setLoading',
              flag: false,
            });
            setTimeout(() => {
              // 将横向滚动条滚动到最右侧
              const scrollElement = document.querySelector('.sm-crumbs-file');
              if (scrollElement) {
                scrollElement.scrollLeft = scrollElement.scrollWidth - scrollElement.clientWidth;
              }
            });
          });
      }
    },
    [dispatch, handoverId],
  );

  const onOk = useCallback(() => {
    switch (stepNumber) {
      case StepNumberType.SelectFile:
        setStepNumber(StepNumberType.ConfirmHandoverFile);
        break;
      case StepNumberType.ConfirmHandoverFile:
        createSnapshot(selectedFiles, handoverId)
          .then((res) => {
            const { token } = res;
            if (token) {
              setToken(token);
              setStepNumber(StepNumberType.ConfirmHandover);
              if (onOK) {
                onOK();
              }
            }
          })
          .catch(() => {
            messageApi.error(s18nText.handovered);
          });
        break;

      default:
        copyHandle(copyContent, messageApi);
        break;
    }
  }, [stepNumber, selectedFiles, handoverId, copyContent, messageApi, onOK, s18nText.handovered]);

  const footer: (
    originNode: React.ReactNode,
    extra: {
      OkBtn: React.FC;
      CancelBtn: React.FC;
    },
  ) => React.ReactNode = (_, { OkBtn, CancelBtn }) => {
    switch (stepNumber) {
      case StepNumberType.ConfirmHandoverFile:
        return (
          <div className={styles.footer}>
            <Button className={styles.footerLeftButton} type="link" onClick={resetStep}>
              {s18nText.previousStep}
            </Button>
            <div className={styles.footerRightButton}>
              <OkBtn />
              <CancelBtn />
            </div>
          </div>
        );
      case StepNumberType.ConfirmHandover:
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );

      default:
        return (
          <div className={styles.footerRightButton}>
            <Tooltip overlayStyle={{ fontSize: 13 }} title={selectedFiles.length === 0 ? s18nText.selectFiles : null}>
              <OkBtn />
              {/* 该span标签影响Tooltip的触发, Tooltip不拿OkBtn当children */}
              <span />
            </Tooltip>
            <CancelBtn />
          </div>
        );
    }
  };

  useEffect(() => {
    switch (stepNumber) {
      case StepNumberType.SelectFile:
        setModalConfig({
          title: s18nText.fileSelect,
          okText: s18nText.nextStep,
          cancelText: s18nText.cancel,
        });
        return;
      case StepNumberType.ConfirmHandoverFile:
        setModalConfig({
          title: s18nText.confirmHandover,
          okText: s18nText.handovering,
          cancelText: s18nText.cancel,
        });
        return;

      default:
        setModalConfig({
          title: s18nText.handovering,
          okText: s18nText.copy,
          cancelText: s18nText.close,
        });
    }
  }, [stepNumber]);

  const onCancel = () => {
    if (propsCancel) {
      propsCancel();
    } else {
      window.parent.postMessage(
        {
          messageType: MessageType.HandoverFilePicker,
          operation: MessageOperation.CloseModal,
        },
        '*',
      );
    }
  };

  return (
    <Modal
      centered
      open
      cancelText={modalConfig?.cancelText}
      className={styles.fileHandoverModal}
      footer={footer}
      mask={!!isModal}
      okButtonProps={{
        disabled: okButtonDisabled,
      }}
      okText={modalConfig?.okText}
      style={
        isModal
          ? undefined
          : {
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              margin: 0,
              padding: 0,
            }
      }
      title={modalConfig?.title}
      width={618}
      onCancel={onCancel}
      onOk={onOk}
    >
      {stepNumber === StepNumberType.SelectFile ? (
        <>
          <Transferee avatar={handoverAvatar} id={id} name={handoverName} />
          <FileSelectionList
            allFiles={handover?.flattenFiles ?? []}
            crumbsList={crumbsList}
            currentFolder={handover?.currentFolder}
            files={handover?.files ?? []}
            loading={!!handover?.loading}
            parentCheck={parentCheck}
            selectedFiles={selectedFiles}
            onChangeSelectedFile={onChangeSelectedFile}
            onClose={onClose}
            onCrumbsClick={onCrumbsClick}
            onPress={onPress}
            onResetSelectedFiles={onResetSelectedFiles}
            onSelectAll={onSelectAll}
          />
        </>
      ) : null}
      {stepNumber === StepNumberType.ConfirmHandoverFile ? (
        <ConfirmationProcess
          files={handover?.flattenFiles ?? []}
          handoverAvatar={handoverAvatar}
          handoverName={handoverName}
          id={id}
          selectedFiles={selectedFiles}
        />
      ) : null}
      {stepNumber === StepNumberType.ConfirmHandover ? (
        <HandoverTips copyContent={copyContent} name={handoverName} />
      ) : null}
      {contextHolder}
    </Modal>
  );
};

export const FileHandoverModalContext: React.FC<{
  isModal?: boolean;
  id?: number;
  name?: string;
  demission?: boolean;
  avatar?: string;
  onCancel?: () => void;
}> = (props: {
  isModal?: boolean;
  id?: number;
  name?: string;
  demission?: boolean;
  avatar?: string;
  onCancel?: () => void;
  onOK?: () => void;
}) => {
  const { id, ...otherProps } = props;
  const handoverId = id ?? parseInt(new URLSearchParams(location.search).get('id') ?? '0', 10);
  return (
    <HandoverProvider uid={handoverId}>
      <FileHandoverModal handoverId={handoverId} {...otherProps} id={id} />
    </HandoverProvider>
  );
};

export default FileHandoverModalContext;

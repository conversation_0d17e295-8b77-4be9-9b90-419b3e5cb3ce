import classNames from 'classnames';
import { memo, useCallback } from 'react';

import type { File } from '@/model/handover';

import styles from './index.less';

interface CrumbsItemProps {
  id: string;
  name: string;
  currentFolder?: File;
  onPress: (id: string) => void;
  selected: boolean;
}

export const CrumbsItem = memo((props: CrumbsItemProps) => {
  const { id, name, onPress, selected, currentFolder } = props;

  const handleClick = useCallback(() => {
    if (onPress && id !== currentFolder?.guid) {
      onPress?.(id);
    }
  }, [onPress, id, currentFolder?.guid]);

  return (
    <div
      className={classNames({
        [styles.crumbsItem]: true,
        [styles.crumbsItemSelected]: !selected,
      })}
      onClick={handleClick}
    >
      {name}
    </div>
  );
});

.fileHandoverManage {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  box-sizing: border-box;
  background: var(--theme-layout-color-bg-white);

  .fileHandoverEmpty {
    margin-top: 240px;
  }

  .fileManagementHeader {
    width: 100%;
    box-shadow: 0 -1px 0 0 var(--theme-box-shadow-color-level10) inset;
    background: var(--theme-bg-color-level1);

    .headerContent {
      width: 1120px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .headerRight {
        display: flex;
        align-items: center;
      }
    }

    :global {
      .ant-layout-header {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50px;
        background: var(--theme-bg-color-level1);
      }
    }
  }

  .fileManagementContent {
    position: relative;
    width: 100%;
    height: calc(100vh - 50px);
    box-sizing: border-box;
    overflow: auto;

    .introduce {
      margin-top: 8px;
      color: var(--theme-text-color-secondary);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }

    .content {
      position: absolute;
      left: calc(50% - 560px);
      width: 1160px;
      min-height: 500px;
      padding: 20px 24px;
      margin: 20px;
      border-radius: 8px;
      border: 1px solid var(--theme-separator-color-lighter);
      background: var(--theme-layout-color-bg-white);
      box-sizing: border-box;
      overflow: hidden;

      @media (max-width: 1120px) {
        left: 0;
      }

      .contentTitle {
        margin-bottom: 8px;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        width: 100%;
        color: var(--theme-text-color-default);

        .contentTitleLeft {
          display: flex;
          align-items: center;

          .failureTime {
            color: var(--theme-text-color-secondary);
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            margin-left: 12px;
          }
        }

        .contentTitleRight {
          display: flex;
          align-items: center;

          .btn {
            border-radius: 2px;
            margin-left: 8px;
          }
        }
      }
    }
  }

  .fileTabs {
    display: flex;
    width: 100%;
    background-color: var(--theme-bg-color-level1);
    border-radius: 8px;
    border: 1px solid var(--theme-separator-color-lighter);
    margin-top: 20px;

    .fileTabBar {
      width: 214px;
      padding: 16px 12px;
      box-sizing: border-box;
      border-right: 1px solid var(--theme-separator-color-lighter);

      .fileTabBarItem {
        width: 190px;

        .fileTabBarItemTitle {
          display: flex;
          padding: 4px 8px;
          align-items: flex-start;
          box-sizing: border-box;
          overflow: hidden;
          color: var(--theme-text-color-secondary);
          text-overflow: ellipsis;
          font-size: 10px;
          font-weight: 400;
          line-height: 16px;
          width: 190px;
        }

        .fileTabBarChildrenItem {
          display: flex;
          padding: 8px;
          align-items: center;
          box-sizing: border-box;
          font-weight: 400;
          width: 100%;
          cursor: pointer;
          border-radius: 4px;
          background: 'transparent';

          &:hover {
            background: var(--theme-menu-color-bg-hover);
          }

          &.fileTabBarChildrenItemChecked {
            background: var(--theme-menu-color-bg-active);

            &:hover {
              background: var(--theme-menu-color-bg-active);
            }

            font-weight: 500;
          }
        }

        .fileTabBarChildrenItemTitle {
          overflow: hidden;
          color: var(--theme-text-color-default);
          text-overflow: ellipsis;
          font-size: 14px;
          line-height: 24px;
          max-width: 150px;
          margin-right: 4px;
        }
      }

      .fileTabBarDivider {
        display: flex;
        padding: 0 8px;
        margin: 4px 0;
        border-bottom: 1px solid var(--theme-separator-color-lighter);
      }
    }

    .tabContent {
      flex: 1;
    }
  }
}

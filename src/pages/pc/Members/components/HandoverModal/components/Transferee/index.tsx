import { UserOutlined } from '@ant-design/icons';
import { Avatar } from 'antd';

import UserCardPopover from '@/components/UserCardPopover';
import { fm } from '@/modules/Locale';

import styles from './index.less';

export const Transferee = ({
  transferee,
  avatar,
  name,
  id,
}: {
  id: string | number | undefined;
  avatar: string;
  name: string;
  transferee?: boolean;
}) => {
  return (
    <div className={styles.transfereeContainer}>
      <div className={styles.transfereeLabel}>
        {transferee ? fm('hanover.currentHandoverPerson') : fm('hanover.transfereePerson')}：
      </div>
      <div className={styles.transfereeUser}>
        <UserCardPopover userId={id as number}>
          <Avatar className={styles.transfereeAvatar} icon={<UserOutlined />} size={24} src={avatar} />
        </UserCardPopover>
        <div className={styles.transfereeName}>{name}</div>
      </div>
    </div>
  );
};

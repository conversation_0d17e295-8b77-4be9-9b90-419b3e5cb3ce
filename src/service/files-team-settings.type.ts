export interface RolePermission {
  targetId: number;
  allow: boolean;
  inherited: boolean;
}

export interface User {
  targetId: number;
  allow: boolean;
  inherited: boolean;
  name: string;
  avatar: string;
  email: string;
  checked?: boolean;
}

export interface PermissionData {
  file_role?: RolePermission[];
  file_role_outsider?: RolePermission[];
  team_role: RolePermission[];
  user?: User[];
}

export interface TeamSettings {
  permissions: PermissionsInfo;
  allowLoosen: boolean;
}

export interface PermissionsInfo {
  canAddOutsider: PermissionData;
  canApplyPermission: PermissionData;
  canChangeShareMode: PermissionData;
  canChangeTeamShareMode: PermissionData;
  canComment: PermissionData;
  canCopy: PermissionData;
  canCreateChildFile: PermissionData;
  canCustomizeAdvancedPermission: PermissionData;
  canDownload: PermissionData;
  canDuplicate: PermissionData;
  canEdit: PermissionData;
  canExit: PermissionData;
  canExport: PermissionData;
  canLock: PermissionData;
  canLockSheetCell: PermissionData;
  canManage: PermissionData;
  canManageAdmin: PermissionData;
  canManageCollaborator: PermissionData;
  canModifyAdvancedPermission: PermissionData;
  canModifyTeamFileTag: PermissionData;
  canModifyUserFileTag: PermissionData;
  canMove: PermissionData;
  canPrint: PermissionData;
  canRead: PermissionData;
  canRemove: PermissionData;
  canRename: PermissionData;
  canSaveAsTemplate: PermissionData;
  canSetPassword: PermissionData;
  canUncompress: PermissionData;
  canUnlock: PermissionData;
}

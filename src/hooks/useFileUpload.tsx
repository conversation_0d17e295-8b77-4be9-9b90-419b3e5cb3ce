import { Button, message, Modal } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { flushSync } from 'react-dom';

import { deleteBulkFile, fileDetail, importFile, importFileProgress } from '@/api/File';
import * as fileApi from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { to } from '@/api/Request';
import type { CloudImportExt } from '@/constants/fileTypes';
import { isSupportedImportExt } from '@/constants/fileTypes';
import { AcceptFileType } from '@/model/Common';
import { CustomEventName } from '@/model/CustomEvent';
import { UserQuota } from '@/model/UserPermission';
import { fm2 } from '@/modules/Locale';
import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import type { ExtraClass, StatusType, UploadItem, UploadType } from '@/store/Upload';
import { useUploadStore } from '@/store/Upload';
import type { FileDetail } from '@/types/api';
import { emitCustomEvent } from '@/utils/customEvent';
import { getGenerateTempId } from '@/utils/file';
import { handleEvent } from '@/utils/tools';
import { sanitizeFilename } from '@/utils/tools';
// 文件上传结果类型
export type FileUploadResult = {
  file?: File;
  status: StatusType;
  message?: string;
  done?: boolean;
  guid?: string;
  generateTempId: string | number;
  fileDetail?: FileDetail;
};

export type FileStatus = {
  item: UploadItem | null;
  progress: number;
};

type UploadOptions = {
  // 上传成功回调
  onSuccess?: (files: FileUploadResult[]) => void;
  // 是否允许选择多个文件
  multiple?: boolean;
  // 允许的文件类型
  accept?: string;
  // 父级目录GUID
  parentGuid?: string;
  // 上传回调
  onProgress?: (fileResult: FileUploadResult) => void;
};

interface ExtendedFile extends File {
  extra?: ExtraClass;
}

export type FolderFileItem = UploadItem & {
  parentId?: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  webkitRelativePath: string;
  status?: StatusType;
  message?: string;
  done?: boolean;
  file: File;
};

export type Folder = FolderFileItem & {
  id: string;
  guid?: string;
  level: number;
  uploadResult?: any;
  resData?: FileDetail;
};
interface ExtendUploadItem extends UploadItem {
  parentGuid: string;
  generateTempId_parent: string;
}

// 文件导入管理类
class FileImportManager {
  private static async pollProgress(
    taskId: string,
    fileGuid: string,
  ): Promise<{ status: StatusType; fileName?: string; errorMsg?: string }> {
    return new Promise((resolve, reject) => {
      const check = async () => {
        const [progressErr, progressRes] = await catchApiResult(importFileProgress(taskId));
        if (progressErr) {
          // 导入失败，删除文件
          try {
            await deleteBulkFile({ fileGuids: [fileGuid] });
          } finally {
            reject({ status: 'fail' as StatusType, errorMsg: progressErr.message });
          }

          return;
        }

        if ((progressRes?.data?.data?.progress ?? 0) >= 100) {
          const [err, res] = await catchApiResult(fileDetail(fileGuid));
          if (err) {
            resolve({
              status: 'fail',
              fileName: err.data.msg,
              errorMsg: err.data.msg,
            });
          } else {
            resolve({
              status: 'success',
              fileName: res?.data.name,
            });
          }
        } else {
          setTimeout(check, 50);
        }
      };

      check();
    });
  }

  static async importFile(
    fileInfo: FileUploadResult,
  ): Promise<{ status: StatusType; message: string; fileName?: string; errorMsg?: string }> {
    const i18n_fileImportTypeErr = fm2('File.importTypeErr');
    const i18n_fileImportErr = fm2('File.importErr');
    const i18n_fileImportSuccess = fm2('File.importSuccess');

    // 验证文件信息
    if (!fileInfo?.fileDetail) {
      return { status: 'fail', message: i18n_fileImportTypeErr };
    }

    if (!isSupportedImportExt(fileInfo.fileDetail.subType || '')) {
      return { status: 'fail', message: i18n_fileImportTypeErr };
    }

    try {
      const fileType = fileInfo.fileDetail.subType || fileInfo.fileDetail.type;
      // 开始导入
      const [importErr, importRes] = await catchApiResult(
        importFile(fileInfo.fileDetail.guid, fileType as CloudImportExt),
      );

      if (importErr) {
        return { status: 'fail', message: i18n_fileImportErr };
      }

      const taskId = importRes?.data.data.taskId;
      if (!taskId) {
        return { status: 'fail', message: i18n_fileImportErr };
      }

      // 轮询进度
      const result = await this.pollProgress(taskId, fileInfo.guid!);

      return {
        status: result.status,
        message: result.status === 'success' ? i18n_fileImportSuccess : i18n_fileImportErr,
        fileName: result.fileName,
        errorMsg: result.errorMsg,
      };
    } catch (error: unknown) {
      return {
        status: 'fail',
        message: i18n_fileImportErr,
        errorMsg: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
/**
 * 文件上传 Hook
 * @param options - 上传配置项
 * @returns UploadResult
 */
export default function useFileUpload(options: UploadOptions = {}) {
  const i18nText = {
    networkError: fm('UseFileUpload.networkError'),
    noSpaceTitle: fm('UseFileUpload.noSpaceTitle'),
    noSpaceContent: fm('UseFileUpload.noSpaceContent'),
    noSpaceOkText: fm('UseFileUpload.noSpaceOkText'),
    limit80Files: fm('UseFileUpload.limit80Files'),
    limit1GFiles: fm('UseFileUpload.limit1GFiles'),
    limitSensitiveWords: fm('UseFileUpload.limitSensitiveWords'),
  };
  const [folders] = useState<Record<string, UploadItem>>(() => ({})); //存储文件夹上传相关的信息
  const { onProgress, multiple = true, accept = '', parentGuid = 'Desktop' } = options;
  const quotas = useMeStore((state) => state.quotas);

  const onProgressRef = useRef(onProgress);

  // 更新 onProgress 引用
  useEffect(() => {
    onProgressRef.current = onProgress;
  }, [onProgress]);

  const [singleResult, setSingleResult] = useState<FileUploadResult | null>(null);
  const [fileStatus, setFileStatus] = useState<FileStatus>({
    progress: 0,
    item: null,
  });
  const { setCurrentUploadList, uploadList, setUploadList, setIsShowUploadBoard } = useUploadStore((state) => state);

  useEffect(() => {
    if (!uploadList.length) return;
    const arr = uploadList.map((item) => {
      if (item.generateTempId === fileStatus.item?.generateTempId) {
        return { ...item, progress: fileStatus.progress };
      }
      return { ...item };
    });
    //获取进度信息 更新list
    setUploadList([...arr]);
  }, [fileStatus, setUploadList]);

  useEffect(() => {
    if (!uploadList.length) return;
    const arr = uploadList.map((item) => {
      if (item.generateTempId === singleResult?.generateTempId) {
        return { ...item, ...singleResult };
      } else {
        return item;
      }
    });
    //获取状态信息 更新list
    const temp = singleResult as ExtendUploadItem;
    if (temp.generateTempId_parent) {
      const list = arr as ExtendUploadItem[];
      // 子任务列表
      const taskList = list.filter(
        (it) => it.type !== 'folder' && it.generateTempId_parent === temp?.generateTempId_parent,
      );
      const doList = taskList.filter((l) => l.done);
      const progress =
        (doList.length /
          ((folders[temp.generateTempId] || folders[temp.generateTempId_parent])?.extra?.fileList.length || 1)) *
        100;
      if (progress >= 100) {
        const generateTempId = folders[temp.generateTempId] ? temp.generateTempId : temp.generateTempId_parent;
        const result = {
          generateTempId: generateTempId,
          done: true,
          progress: 100,
          message: fm2('UseFileUploadMobile.uploadSuccess'),
          file: temp.file,
          status: 'success' as StatusType,
        };
        setSingleResult(result);
      } else {
        setFileStatus({
          progress: progress,
          item: list.find(
            (l) => l.generateTempId === (temp.type === 'folder' ? temp.generateTempId : temp.generateTempId_parent),
          ) as UploadItem,
        });
      }
    }
    setUploadList([...arr]);
  }, [setUploadList, singleResult]);

  // 上传文件主函数
  const uploadFile = useCallback(
    async (item: UploadItem): Promise<FileUploadResult> => {
      //变更状态 为上传
      const list = uploadList;
      const index = list.findIndex((it) => item.generateTempId === it.generateTempId);
      list[index] = { ...item, status: 'uploading' };
      setUploadList([...list]);

      // 第一步：获取上传策略
      const formData_1 = new FormData();
      formData_1.append('encodedFileName', item.name);
      formData_1.append('file', item.file);
      const headers = {
        'x-file-parent-guid': item.parentGuid ? item.parentGuid : parentGuid,
        'x-file-size': String(item.size),
        'x-file-type': item.type,
        'x-requested-with': 'XMLHttpRequest',
      };

      const [error1, postPolicyRes] = await to(
        fileApi.uploadPostPolicy(
          formData_1,
          {
            ...headers,
            'x-file-parent-guid': headers['x-file-parent-guid'] || '',
          },
          item.controller,
        ),
      );
      if (error1 || postPolicyRes?.status !== 200) {
        return {
          generateTempId: item.generateTempId,
          file: item.file,
          status: error1?.message === 'canceled' ? 'cancel' : 'fail',
          done: true,
          message: error1?.data?.msg || postPolicyRes?.data?.message || i18nText.networkError,
        };
      }
      const { url, formData: formDataParams } = postPolicyRes.data.uploadUrl;

      // 第二步：上传文件到Minio
      const formData = new FormData();
      Object.entries(formDataParams || {}).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append('file', item.file);
      const [error2, uploadRes] = await to(
        fileApi.uploadMinio(url, formData, item.controller, (progress) => {
          setFileStatus({ progress, item });
        }),
      );
      if (error2 || uploadRes?.status !== 200) {
        return {
          generateTempId: item.generateTempId,
          file: item.file,
          status: error2?.message === 'canceled' ? 'cancel' : 'fail',
          done: true,
          message: error2?.data?.msg || uploadRes?.data?.message || i18nText.networkError,
        };
      }

      // 第三步：上传回调
      const callbackData = {
        bucket: formDataParams.bucket,
        fname: item.name,
        size: item.size,
        key: formDataParams.key,
        token: formDataParams['x:token'],
      };

      const [error3, callbackRes] = await to(fileApi.uploadCallback(callbackData, item.controller));
      if (error3 || callbackRes?.status !== 200) {
        return {
          generateTempId: item.generateTempId,
          file: item.file,
          status: error3?.message === 'canceled' ? 'cancel' : 'fail',
          done: true,
          message: error3?.data?.msg || callbackRes?.data?.message || i18nText.networkError,
        };
      }
      // 如果是成功状态，需要判断当前是否是导入文件，如果是导入文件，需要将状态改为 progress
      const obj: { status: StatusType; name?: string } = {
        status: 'success',
      };
      if (item.uploadType === 'import') {
        const progressObj = {
          generateTempId: item.generateTempId,
          file: item.file,
          status: 'progress' as StatusType,
          done: true,
          guid: callbackRes?.data?.guid || '',
          fileDetail: callbackRes?.data,
        };

        // 先返回 progress 状态
        onProgressRef.current?.(progressObj);
        flushSync(() => {
          setSingleResult(progressObj);
        });

        // 然后执行导入并返回最终状态
        const result = await FileImportManager.importFile(progressObj);
        obj.status = result.status;
        // 导入成功后，需要更新文件名
        obj.name = result.fileName;
      }

      if (!item.generateTempId_parent) {
        emitCustomEvent(CustomEventName.uploadRefreshFileList, { item: callbackRes?.data }); //触发fileList更新事件
      }
      return {
        generateTempId: item.generateTempId,
        file: item.file,
        status: obj.status,
        done: true,
        guid: callbackRes?.data?.guid || '',
        fileDetail: {
          ...callbackRes?.data,
          name: obj.name || callbackRes?.data?.name,
        },
      };
    },
    [i18nText.networkError, parentGuid, setUploadList, uploadList],
  );

  //上传全部文件夹 树形结构
  const uploadFolderData = useCallback(
    async (uploadObj: UploadItem, folderList: Folder[], controller: AbortController | null): Promise<Folder> => {
      let end = false;
      const folderListCopy = [...folderList].sort((a, b) => a.level - b.level);
      let item: Folder = { ...folderListCopy[0], id: '', parentId: '', status: 'success' }; // 初始化默认值
      let result: Folder = { ...folderListCopy[0], id: '', parentId: '', status: 'success' }; // 结果值

      while (!end) {
        const list = folderListCopy.filter((l) => !l.guid);
        if (list.length === 1) {
          end = true;
        }
        const it = list[0];

        const [error, res] = await to(
          fileApi.createFolder(
            {
              folder: it.level === 1 ? parentGuid : folderListCopy.find((l) => l.id === it.parentId)?.guid || 'guid',
              name: it.name,
              type: 'folder',
            },
            controller,
          ),
        );

        if (it.level === 1) {
          //父文件夹信息存储起来
          folderListCopy[0].resData = res?.data || {};
        }
        if (res?.status !== 200) {
          result = {
            ...folderListCopy[0],
            guid: '',
            status: error?.message === 'canceled' ? 'cancel' : 'fail',
            message: fm2('UploadBoard.folderUploadError'),
            done: true,
          };
          item = {
            ...it,
            guid: '',
            status: error?.message === 'canceled' ? 'cancel' : 'fail',
            message: fm2('UploadBoard.folderUploadError'),
            done: true,
          };
          end = true;
        } else {
          result = {
            ...folderListCopy[0],
            guid: res.data.guid,
            status: 'success',
          };
          item = {
            ...it,
            guid: res.data.guid,
            status: 'success',
          };
        }

        const folderIndex = folderListCopy.findIndex((l) => l.id === it.id);
        folderListCopy[folderIndex] = item;

        if (folders[uploadObj.generateTempId]?.extra) {
          // 使用非空断言操作符进行赋值
          (folders[uploadObj.generateTempId] as { extra: { folderList: typeof folderListCopy } }).extra.folderList =
            folderListCopy;
        }
      }

      return result;
    },
    [folders, parentGuid],
  );

  // 上传文件夹主函数
  const uploadFolder = useCallback(
    async (item: UploadItem): Promise<FileUploadResult> => {
      //变更状态 为上传
      const list = uploadList;
      const index = list.findIndex((it) => item.generateTempId === it.generateTempId);
      list[index] = { ...item, status: 'uploading' };
      setUploadList([...list]);

      // 第一步：上传所有文件夹
      const folderList = item.extra?.folderList || [];
      const result1 = await uploadFolderData(item, folderList, item.controller);
      if (!result1.guid) {
        //没有guid说明是文件夹上传失败
        return { ...result1, generateTempId: item.generateTempId } as FileUploadResult;
      }
      emitCustomEvent(CustomEventName.uploadRefreshFileList, { item: result1?.resData }); //触发fileList更新事件
      // return { ...result1, generateTempId: item.generateTempId } as FileUploadResult;

      // 第二步：上传所有文件
      let fileList = (folders[item.generateTempId] as { extra: { fileList: FolderFileItem[] } }).extra.fileList;
      const folderListT = item.extra?.folderList || [];
      fileList =
        fileList.map((l) => ({
          ...l,
          parentGuid: folderListT.find((m) => m.id === l.parentId)?.guid || parentGuid,
          hidden: true,
        })) || [];
      const uploads = [...fileList, ...uploadList];
      setUploadList(uploads);
      return { ...result1, status: 'uploading', generateTempId: item.generateTempId } as FileUploadResult;
    },
    [setUploadList, uploadFolderData, uploadList],
  );

  useEffect(() => {
    const list = uploadList;
    if (list.length) {
      const waitingFileList = list.filter((item) => item.status === 'waiting');
      if (waitingFileList.length) {
        const fetchData = async (item: FolderFileItem) => {
          if (item.uploadType === 'folder') {
            try {
              const res = await uploadFolder(item);
              const resObj = { ...singleResult, ...item, ...res };
              flushSync(() => {
                setSingleResult(resObj);
              });
            } catch (error) {}
          } else {
            try {
              const res = await uploadFile(item);
              const resObj = { ...singleResult, ...item, ...res, done: true };
              onProgressRef.current?.(resObj);
              flushSync(() => {
                setSingleResult(resObj);
              });
            } catch (error) {}
          }
        };
        const uploadingFileList = list.filter((item) => item.status === 'uploading');
        if (uploadingFileList.length <= 10) {
          //限制同时上传 10个文件
          fetchData(waitingFileList[0] as FolderFileItem);
        }
      }
    }
  }, [uploadFile, uploadList]);

  const handleFilesToUploadListFunc = useCallback(
    async (
      resList: UploadItem[],
      files: ExtendedFile[],
      // 上传类别：文件上传、文件导入、文件夹上传。文件上传不限制上传内容；文件导入只能上传指定的文件格式
      uploadType: UploadType = 'all',
    ) => {
      const list = [];
      for (let i = 0; i < files.length; i++) {
        const generateTempId = getGenerateTempId();
        const fileInfo = {
          type: files[i].type,
          size: files[i].size,
          file: files[i],
          generateTempId: generateTempId,
          name: sanitizeFilename(files[i].name),
          parentGuid: parentGuid,
          status: 'waiting' as StatusType,
          controller: new AbortController(),
          uploadType: uploadType,
          extra: {
            folderList: (files[i].extra?.folderList || []).map((l) => {
              return { ...l, generateTempId_parent: generateTempId, generateTempId: getGenerateTempId() };
            }),
            fileList: (files[i].extra?.fileList || []).map((l) => {
              return { ...l, generateTempId_parent: generateTempId, generateTempId: getGenerateTempId() };
            }),
          },
        };
        if (uploadType === 'folder') {
          resList.unshift(fileInfo);
          folders[fileInfo.generateTempId] = fileInfo;
        } else {
          resList.unshift(fileInfo);
          list.push(fileInfo);
        }
      }
      setCurrentUploadList(list);
      setUploadList([...resList]);
    },
    [parentGuid, setCurrentUploadList, setUploadList],
  );

  const showNoSpace = useCallback(() => {
    Modal.confirm({
      icon: null,
      title: i18nText.noSpaceTitle,
      content: i18nText.noSpaceContent,
      width: 400,
      footer: (_) => {
        return (
          <div className="sm-footer">
            <Button
              className="sm-btn sm-btn-normal-primary"
              onClick={() => {
                Modal.destroyAll();
              }}
            >
              {i18nText.noSpaceOkText}
            </Button>
          </div>
        );
      },
    });
  }, [i18nText.noSpaceContent, i18nText.noSpaceOkText, i18nText.noSpaceTitle]);

  const ischeckSpaceSizeOk = useCallback(
    async (arr: File[] | { size: number }[]) => {
      let totalSize = 0;
      arr.forEach((it: { size: number }) => {
        totalSize += Number(it.size || 0);
      });
      const [error, callbackRes] = await to(fileApi.getFileQuota());
      if (error) {
        message.error(i18nText.networkError);
        return { noNetwork: true };
      }
      const remaining = callbackRes?.data?.personalDiskVolume?.remaining || 0;
      return { noNetwork: false, onNeedSpaceSize: Number(remaining) > totalSize };
    },
    [i18nText.networkError],
  );

  const calculateFilesSize = useCallback(
    (files: File[]) => {
      const total = files.reduce((total, file) => total + file.size, 0);
      return total - (quotas[UserQuota.cloud_team_space_upload_file_size]?.max ?? 0) * 1024 * 1024 > 0;
    },
    [quotas],
  );

  //检测是否含有敏感词
  const checkHaveSensitiveWords = useCallback((files: File[], key: string) => {
    //页面注入的敏感词
    const sensitiveWords = window.upload_sensitive_words || '';
    if (sensitiveWords) {
      const sensitiveWordsList = sensitiveWords.split(',');
      for (let i = 0; i < files.length; i++) {
        const fileName = key === 'name' ? files[i].name : files[i].webkitRelativePath;
        for (let j = 0; j < sensitiveWordsList.length; j++) {
          if (fileName.includes(sensitiveWordsList[j])) {
            return true;
          }
        }
      }
    }
    return false;
  }, []);

  // 触发文件选择
  const triggerUpload = useCallback(() => {
    // 创建文件选择器
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = multiple;
    fileInput.accept = accept;
    fileInput.style.display = 'none';

    const handleFileChange = async (event: Event) => {
      const files = (event.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;
      let sliceFiles: File[] = Array.from(files);

      if (sliceFiles && sliceFiles.length > 80) {
        message.warning(i18nText.limit80Files);
        sliceFiles = sliceFiles.slice(0, 80);
      }

      //检测是否含有敏感词
      if (checkHaveSensitiveWords(sliceFiles, 'name')) {
        message.warning(i18nText.limitSensitiveWords);
        return;
      }

      //计算上传大小 大于1G取消上传
      if (calculateFilesSize(sliceFiles)) {
        message.warning(i18nText.limit1GFiles);
        return;
      }

      const resList = uploadList;
      //空间问题
      const { noNetwork, onNeedSpaceSize } = await ischeckSpaceSizeOk(Array.from(sliceFiles));
      if (noNetwork) return;
      if (!onNeedSpaceSize) {
        showNoSpace();
        return;
      }

      setIsShowUploadBoard(true);
      handleFilesToUploadListFunc(resList, sliceFiles, 'all');
    };

    // 监听文件选择事件
    fileInput.addEventListener('change', handleFileChange);

    // 触发文件选择器点击
    document.body.appendChild(fileInput);
    fileInput.click();

    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(fileInput);
    }, 100);
  }, [
    accept,
    calculateFilesSize,
    handleFilesToUploadListFunc,
    i18nText.limit1GFiles,
    i18nText.limit80Files,
    ischeckSpaceSizeOk,
    multiple,
    setIsShowUploadBoard,
    showNoSpace,
    uploadList,
  ]);

  const extractFolderStructure = useCallback((files: File[]): ExtraClass => {
    // 存储文件夹信息的对象
    const folders: Record<string, Folder> = {};
    const fileList: FolderFileItem[] = [];
    // 处理每个文件的路径
    files.forEach((file) => {
      const relativePath = file.webkitRelativePath;
      if (!relativePath) return; // 跳过没有相对路径的文件

      // 分割路径为文件夹数组
      const pathParts = relativePath.split('/');
      // 移除最后一个元素（文件名），保留文件夹部分
      const folderParts = pathParts.slice(0, pathParts.length - 1);

      let currentPath = '';
      let parentId: string = '';

      folderParts.forEach((folderName, index) => {
        // 构建当前文件夹的完整路径
        currentPath += (currentPath ? '/' : '') + folderName;

        // 如果文件夹不存在，则添加到结果中
        if (!folders[currentPath]) {
          folders[currentPath] = {
            ...file,
            id: currentPath, // 使用完整路径作为唯一标识
            name: folderName, // 文件夹名称
            parentId: parentId, // 父文件夹ID
            level: index + 1, // 文件夹层级（根为1）
            lastModified: file.lastModified,
            file: file,
            size: 0,
            type: 'folder',
            webkitRelativePath: file.webkitRelativePath,
          } as unknown as Folder;
        }

        // 更新父文件夹ID为当前文件夹ID，继续处理下一级文件夹
        parentId = currentPath;
      });
      const tempFile = {
        parentId: folderParts.join('/'),
        name: file.name,
        size: file.size,
        lastModified: file.lastModified,
        type: file.type,
        file: file,
        status: 'waiting' as StatusType,
        webkitRelativePath: file.webkitRelativePath,
      } as FolderFileItem;
      fileList.push(tempFile);
    });

    // 将结果转换为数组并返回
    return { folderList: Object.values(folders), fileList: fileList };
  }, []);

  const triggerUploadFolder = useCallback(() => {
    // 创建文件夹选择器
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = multiple;
    fileInput.accept = accept;
    fileInput.webkitdirectory = true;
    fileInput.style.display = 'none';

    const handleFileChange = async (e: Event) => {
      const files = Array.from((e.target as HTMLInputElement).files || []);
      if (files.length === 0) return;
      let sliceFiles: File[] = Array.from(files);

      if (sliceFiles && sliceFiles.length > 80) {
        message.warning(i18nText.limit80Files);
        sliceFiles = sliceFiles.slice(0, 80);
      }

      //检测是否含有敏感词
      if (checkHaveSensitiveWords(sliceFiles, 'webkitRelativePath')) {
        message.warning(i18nText.limitSensitiveWords);
        return;
      }

      //计算上传大小 大于1G取消上传
      if (calculateFilesSize(sliceFiles)) {
        message.warning(i18nText.limit1GFiles);
        return;
      }

      const { fileList, folderList } = extractFolderStructure(sliceFiles);

      //空间问题
      const { noNetwork, onNeedSpaceSize } = await ischeckSpaceSizeOk(Array.from(sliceFiles));
      if (noNetwork) return;
      if (!onNeedSpaceSize) {
        showNoSpace();
        return;
      }

      const templateFolder = folderList.find((it) => !it.parentId);
      let totalSize = 0;
      fileList.forEach((it) => {
        totalSize += Number(it.size || 0);
      });

      const resList = uploadList;
      const folder = {
        name: templateFolder?.name,
        lastModified: templateFolder?.lastModified,
        webkitRelativePath: templateFolder?.webkitRelativePath,
        size: totalSize,
        type: 'folder',
        extra: {
          folderList,
          fileList,
        },
      } as ExtendedFile;

      handleFilesToUploadListFunc(resList, [folder], 'folder');
      setIsShowUploadBoard(true);
    };

    // 监听文件选择事件
    fileInput.addEventListener('change', handleFileChange);

    // 触发文件选择器点击
    document.body.appendChild(fileInput);
    fileInput.click();

    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(fileInput);
    }, 100);
  }, [
    accept,
    extractFolderStructure,
    handleFilesToUploadListFunc,
    i18nText.limit80Files,
    ischeckSpaceSizeOk,
    multiple,
    setIsShowUploadBoard,
    showNoSpace,
    uploadList,
  ]);

  // 文件导入
  const importFile = useCallback(
    (type: UploadType, noSpace: () => void) => {
      const a = document.createElement('input');
      a.type = 'file';
      a.multiple = true;
      a.accept = type === 'all' ? accept : AcceptFileType;
      a.click();

      setCurrentUploadList([]);

      const removeListener = handleEvent(a, 'change', async (event: Event) => {
        const files = (event.target as HTMLInputElement).files;
        if (!files || files.length === 0) return;
        let sliceFiles: File[] = Array.from(files);

        if (sliceFiles && sliceFiles.length > 80) {
          message.warning(i18nText.limit80Files);
          sliceFiles = sliceFiles.slice(0, 80);
        }

        const resList = uploadList;
        //空间问题
        const { noNetwork, onNeedSpaceSize } = await ischeckSpaceSizeOk(sliceFiles);
        if (noNetwork) return;
        if (!onNeedSpaceSize) {
          noSpace?.();
          return;
        }

        setIsShowUploadBoard(true);
        handleFilesToUploadListFunc(resList, sliceFiles, type);
        removeListener();
      });
    },
    [uploadList, i18nText.limit80Files, setIsShowUploadBoard],
  );

  return {
    triggerUpload,
    triggerUploadFolder,
    importFile,
  };
}

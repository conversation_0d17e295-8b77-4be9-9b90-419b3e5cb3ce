export default {
  'hooks.Authorization.loginFailedMessage': 'Login failed',
  'service.Me.anonymousName': 'Anonymous',
  'Common.previewLoading': 'Loading preview...',
  'Common.loadingText': 'The system is processing, please wait...',
  'Common.confirm': 'Confirm',
  'Common.cancel': 'Cancel',
  'File.docx': 'Word Document',
  'File.file': 'File',
  'File.document': 'Document',
  'File.newdoc': 'Document',
  'File.modoc': 'Classic Docs',
  'File.mosheet': 'Spreadsheet',
  'File.presentation': 'Presentation',
  'File.table': 'Table',
  'File.form': 'Form',
  'File.normalForm': 'Basic Form',
  'File.tableViewForm': 'Table-view Form',
  'File.quizForm': 'Quiz Form',
  'File.folder': 'Folder',
  'File.mindMap': 'Mind Map',
  'File.mindmap': 'Mind Map',
  'File.board': 'Whiteboard',
  'File.spreadsheet': 'spreadsheet',
  'File.xmind': 'XMIND file',
  'File.wps': 'WPS file',
  'File.mp4': 'video',
  'File.zip': 'compressed files',
  'File.mp3': 'audio',
  'File.ppt': 'ppt',
  'File.pdf': 'PDF',
  'File.upload': 'Uploaded Files',
  'File.all': 'All Types',
  'File.sortByNameAZ': 'Name (A-Z)',
  'File.sortByNameZA': 'Name (Z-A)',
  'File.sortByNewest': 'Newest First',
  'File.sortByOldest': 'Oldest First',
  'File.sortBySmallest': 'Smallest First',
  'File.sortByLargest': 'Largest First',
  'File.sortByUpdateTime': 'Latest Update',
  'File.sortFolderTop': 'Folders on Top',
  'File.setSort': 'Set Sort',
  'File.emptyFavorites':
    'For files without favorites, you can add documents, tables, or any file to favorites in the file menu',
  'File.emptyRecycle': 'No documents in the recycling bin',
  'File.emptyDesktop': 'No file content',
  'File.emptyShare': 'No collaboration invitations have been received yet',
  'File.emptySubShare': 'You can see the files and folders you have added as a collaborator here',
  'File.emptyCreate': 'No files created',

  'File.shared': 'Shared',
  'File.bySharingTime': 'By sharing time',
  'File.byCreationTime': 'By creation time',
  'File.picture': 'Picture',
  'File.package': 'Package',
  'File.shortcut': 'Shortcut',
  'File.downloadFor': 'Download for',

  // tab页签
  'Tab.home': 'Home',
  'Tab.file': 'File',
  'Tab.notice': 'Notice',
  'Tab.my': 'My',
  'Tab.returnToFile': 'Return to file page',

  'Login.loginTitle': 'Login',
  'Login.autoLanguageSetting': 'Auto',
  'Login.autoThemeSetting': 'Auto',
  'Login.lightThemeSetting': 'Light mode',
  'Login.darkThemeSetting': 'Dark mode',
  'LoginView.userNameInputPlaceholder': 'Enter your account',
  'LoginView.passwordInputPlaceholder': 'Enter your password',
  'LoginView.ldapLoginUserNameInputPlaceholder': 'Enter your account',
  'LoginView.loginButtonText': 'Login',
  'LoginView.userNameInputLabel': 'Email',
  'LoginView.ldapLoginUserNameInputLabel': 'Account',
  'LoginView.department': 'Department',
  'LoginView.loginError': '帐号或密码错误',
  'LoginView.passwordInputLabel': 'password',
  'LoginView.emailFormatInaccurate': 'Invalid email format',
  'MessageCenter.onlyreadButtonText': 'Show read only',
  'MessageCenter.onlyUnreadButtonText': 'Show unread only',
  'MessageCenter.allMarkReadButtonText': 'Mark all as read',
  'UserCenter.settings': 'Personal settings',
  'UserCenter.myBusiness': 'My organization',
  'UserCenter.managementBusiness': 'Corporate Management',
  'UserCenter.switchLanguages': 'Switch languages',
  'UserCenter.logOut': 'Log out',
  'UserCenter.myDesktopCapacit': 'My desktop capacity',
  'UserCenter.totalEnterpriseCapacity': 'Total enterprise capacity',
  'SearchCenter.me': 'Me',
  'SearchCenter.update': 'Update',
  'SearchCenter.open': 'Open',
  'SearchCenter.searchFile': 'Search file',
  'SearchCenter.noData': 'No search results',
  'SearchCenter.used': 'Recently',
  'SearchCenter.to': 'To',
  'SearchCenter.alLocations': 'All locations',
  'SearchCenter.forTitle': 'Search for the title',
  'SearchCenter.forContent': 'Search content',
  'SearchCenter.fileType': 'Filter type',
  'SearchCenter.category': 'Present the results',
  'SearchCenter.searchFields': 'Match range',
  'SearchCenter.all': 'All',
  'SearchCenter.onlyShare': 'Just look at what was shared with me',
  'SearchCenter.shearchCreator': 'Search for the creator',
  'SearchCenter.searchFieldsAll': 'Title content only',
  'SearchCenter.searchFieldsName': 'Title only',
  'SearchCenter.uploadedFiles': 'Uploaded files',
  'SearchCenter.currentSpace': 'Current space',

  'Header.backButtonTipText': 'Back',
  'Header.backToButtonTipText': 'Back to',
  'Header.createButtonTipText': 'Create',
  'Header.inputPlaceholder': 'Untitled',
  'Header.teamButtonText': 'Collaborate',
  'Header.shareButtonText': 'Share',
  'Header.fileMenuButtonTipText': 'File menu',
  'Header.historyButtonText': 'History',
  'Header.demoButtonText': 'Presentation',
  'Header.followingModeText': 'Following mode',
  'Header.editButtonText': 'Edit',
  'Header.noAuthEdit': "You don't have permission to edit this file. Please contact the file owner for access.",
  'Header.downloadButtonText': 'Download',
  'Header.sharingCollaborationButtonText': 'Share & Collaborate',
  'Editor.saveTemplate': 'Save as template',
  'Editor.toOnlineFile': 'Convert to online file',
  'Editor.fileLoaded': 'File loaded',
  'Editor.openLater': 'Open later',
  'Editor.openFile': 'Open file',
  'Editor.loadingFile': 'Loading file',
  'Editor.loadingFileError': 'Failed to load file',
  'Editor.saveStatus.offlineSaving': 'Saving offline content',
  'Editor.saveStatus.offlinePersistSucceed': 'Edits saved offline, will sync when connected',
  'Editor.saveStatus.offline': 'No network connection, content will be saved offline',
  'Editor.saveStatus.offlinePersistFailed': 'Offline save failed',
  'Editor.saveStatus.online': 'Content will be saved automatically',
  'Editor.saveStatus.onlineSaving': 'Saving',
  'Editor.saveStatus.saveAccepted': 'Saving',
  'Editor.saveStatus.saveSucceed': 'Auto-save successfully',
  'Editor.saveStatus.applySucceed': 'Content updated automatically',
  'Editor.saveStatus.saveFailed': 'Save failed',
  'Editor.saveStatus.applyFailed': 'Save failed',
  'Editor.saveStatus.saveTimeout': 'Save failed',
  'Editor.saveStatus.acceptTimeout': 'Save failed',
  'Editor.syncStatus.syncSaving': 'Syncing offline data, please wait...',
  'Editor.syncStatus.syncSucceed': 'Offline data synced, content will auto-save',
  'Editor.syncStatus.syncFailed': 'Save failed',
  'Editor.noSupport': 'This feature is not supported yet',
  'Editor.ok': 'Got it',
  'Editor.syncSaving': 'Syncing offline data, please wait...',
  'Editor.syncSucceed': 'Offline data synced, content will auto-save',
  'Editor.syncFailed': 'Offline data sync failed',
  'Editor.noEditing': 'Editing is disabled',
  'Editor.noEditingContent': "You don't have edit access. Contact file owner",
  'Editor.sorry': 'Sorry...',
  'Editor.fileDeleted': 'File has been deleted',
  'Editor.saveFailed': 'Save failed',
  'Editor.saveFailedContent': 'Save failed. Copy content and refresh',
  'Editor.yourPermission': 'Your permission is:',
  'Editor.canEdit': 'Can edit',
  'Editor.canComment': 'Can comment',
  'Editor.canRead': 'Read only',
  'Editor.onlyReadAndComment':
    'The current device only supports reading and commenting. If you need to edit, please open it on your computer.',
  'Header.favorite': 'Favorite',
  'Header.unfavorite': 'Unfavorite',
  'Header.favorited': 'Favorited',
  'Header.unfavorited': 'Unfavorited',
  'Header.unSubscribed': 'You have already unsubscribed from that {type}',
  'Header.subscribed':
    'Subscriptions are turned on, and a notification message will be sent to you when the {type} is updated',
  'BackToPopover.searchFiles': 'Search files...',
  'BackToPopover.backTo': 'Back to',
  'BackToPopover.myDesktop': 'My Desktop',
  'BackToPopover.workbench': 'Workbench',
  'BackToPopover.recentlyUsed': 'Recently used',
  'BackToPopover.quickAccess': 'Quick access',
  'AvatarGroup.restCount': '{count} more collaborators',
  'SiderMenu.siderMenuCreactText': 'Create',
  'SiderMenu.createDisablePopText': 'No permission to create folders. Contact admin',
  'SiderMenu.new': 'New',
  'SiderMenu.siderMenuRecentText': 'Recent files',
  'SiderMenu.siderMenuShareText': 'Shared with me',
  'SiderMenu.siderMenuFavoritesText': 'My favorites',
  'SiderMenu.siderMenuDesktopText': 'My desktop',
  'SiderMenu.siderMenuSpaceText': 'Team space',
  'SiderMenu.siderMenuTrashText': 'Trash',
  'SiderMenu.siderMenuBusinessText': 'Organization settings',
  'SiderMenu.siderMenuCreateDocText': 'Document',
  'SiderMenu.siderMenuCreateHome': 'There are no junk files yet',
  'SiderMenu.siderMenuCreateMoDocText': 'Classic Docs',
  'SiderMenu.siderMenuCreateTableText': 'Spreadsheet',
  'SiderMenu.siderMenuCreateMoTableText': 'Table',
  'SiderMenu.siderMenuCreatePptText': 'Presentation',
  'SiderMenu.siderMenuCreateFormText': 'Form',
  'SiderMenu.siderMenuCreateOrdinaryFormText': 'Basic Form',
  'SiderMenu.siderMenuCreateTableFormText': 'Table-view Form',
  'SiderMenu.siderMenuCreateTestFormText': 'Quiz Form',
  'SiderMenu.siderMenuCreateFolderText': 'Folder',
  'SiderMenu.siderMenuCreateSpace': 'Space',
  'SiderMenu.siderMenuCreateBlank': 'Whiteboard',
  'SiderMenu.siderMenuCreateThink': 'Mind map',
  'SiderMenu.siderMenuCreateCloud': 'Cloud file',
  'SiderMenu.siderMenuCreateSpaceText': 'Space',
  'SiderMenu.siderMenuCreateUploadFolderText': 'Upload folders',
  'SiderMenu.siderMenuCreateUploadFileText': 'Upload files',
  'SiderMenu.siderMenuCreateFileUploadText': 'Files upload',
  'SiderMenu.siderMenuCreateMindMapText': 'Mind Map',
  'SiderMenu.siderMenuCreateTemplateText': 'Create from Template Library',
  'SiderMenu.siderMenuTemplateCreateText': 'Template Library create',
  'SiderMenu.cancel': 'Cancel',
  'SiderMenu.confirm': 'Confirm',
  'SiderMenu.inputName': 'Input name',
  'SiderMenu.pleaseInputFilename': 'Please enter a file name',
  'SiderMenu.validateIllegalTips': 'The file name must not contain the following illegal characters',
  'PcError.createErrorText': 'Create error',
  'PcError.unknownErrorText': 'Unknown error',
  'CreateFileMenu.networkStatusTipText': 'Please check your network',
  'CreateFolder.folderTitle': 'New folder',
  'CreateFolder.saveVersionTitle': 'Save version',
  'CreateFolder.spaceTitle': 'New team space',
  'CreateFolder.folderPlaceholder': 'Enter folder name',
  'CreateFolder.spacePlaceholder': 'Enter space name',
  'CreateFolder.ruleMessage': 'Please complete all required fields',
  'CreateFolder.saveVersionPlaceholder': 'Enter version name',
  'Editor.openInNewTab': 'Open in new tab',
  'AddNewPopover.templateLibrary': 'Template library',
  'AddNewPopover.upload': 'Upload',
  'AddNewPopover.uploadFolder': 'Upload folder',
  'Error.loginGuideTitle': 'Please log in to access',
  'Error.loginText': 'Log in',
  'Error.notFound': 'Page not found',
  'Error.notFoundDes': 'The page you visited does not exist',
  'Error.goBackHomePage': 'Go back to desktop',
  'Error.fileDeleteTitle': 'File deleted',
  'Error.folderDeleteTitle': 'The folder was deleted.',
  'Error.folderDeleteSubTitle': 'The folder you visited has been deleted',
  'Error.fileDeleteSubTitle': 'The file you accessed has been deleted',
  'Error.switchAccount': 'Switch account',
  'Error.accessRestrictedTitle': 'Login required',
  'Error.noSeatsTitle': 'No available seat',
  'Error.noSeatsTitleDes': 'You have no access seat. Contact admin to assign or purchase',
  'Error.contactAdmin': 'Contact administrator',
  'Error.purchaseSeats': 'Purchase seats',
  'Error.unknownErrorTitle': 'Page failed to load',
  'Error.unknownErrorSubTitle': 'Please refresh the page with a good network',
  'Error.netErr': 'Network error',
  'Error.netErrDes': 'Check network and try again',
  'Error.refresh': 'Refresh',
  'Error.noLogin': "You're not logged in.",
  'Error.noLoginDes': 'Document owner set restricted access. Please log in',
  'Error.noPermission': 'No access permission',
  'Error.noPermissionDes': "Document owner hasn't added you. Contact for access",
  'Error.noPermissionDesH5': 'User {user} has no access to this document',
  'Error.applyPermissionSentPC': 'Application for permission has been sent to the administrator',
  'Error.applyPermissionSent': 'Application for permission has been sent to the administrator',
  'Error.applyPermissionSentPrefix': 'Already',
  'Error.applyPermissionSentSuffix': 'sent application to',
  'Error.applyPermissionSentManager': 'administrator',
  'Error.applyPermissionSentSuccess': 'Your application has been sent successfully',
  'Error.applyForPermission': 'Request document access',
  'Error.selectPermissionType': 'Please select access type',
  'Error.addRemarkPlaceholder': 'Optional remark',
  'Error.sendApplication': 'Send request',
  'Error.permissionReadable': 'Readable',
  'Error.permissionCommentable': 'Commentable',
  'Error.permissionEditable': 'Editable',
  'Error.applyForPermissionBtn': 'Apply for access permission',
  'Error.noPermissionWithUserLine1':
    'The current logged-in account {username} does not have permission to access this document.',
  'Error.noPermissionWithUserLine2':
    'To request comment, edit, or admin permissions, please contact {depNames} to apply.',
  'Error.subtitleFolder': 'The current logged-in account does not have permission to access this directory',
  'Error.applyTooFrequent': 'Too many requests. Try again in 2 minutes',
  'Forbidden.title': 'Account deactivated',
  'Forbidden.tips': '{email}, your organization account is deactivated. Contact admin to restore it',
  'deleteConfirm.title': 'Delete confirmation',
  'deleteConfirm.content': 'Delete this file? All collaborators will lose access',

  // 用户被禁用页面

  'deleteConfirm.mobile-title': 'Are you sure to delete {typeName}?',
  'deleteConfirm.mobile-share': 'Are you sure to exit {typeName}?',
  'deleteConfirm.cancel': 'Cancel',
  'deleteConfirm.success': 'Deleted successfully',
  'deleteConfirm.error': 'Delete failed',
  'useFileDetail.creator': 'Creator',
  'useFileDetail.modocTitle': 'Document info',
  'useFileDetail.mosheetTitle': 'Spreadsheet info',
  'useFileDetail.tableTitle': 'Table info',
  'useFileDetail.pptTitle': 'Presentation info',
  'useFileDetail.formTitle': 'Form info',
  'useFileDetail.mindmapTitle': 'Mind map info',
  'useFileDetail.mindmapTopicCount': 'Topic count',
  'useFileDetail.mindmapWordCount': 'Character count',
  'useFileDetail.statisticWordCount': 'Total words',
  'useFileDetail.charCount': 'Character count (with spaces)',
  'useFileDetail.charCountWithoutSpaces': 'Character count (no spaces)',
  'useFileDetail.page': 'Pages',
  'useFileDetail.total': 'Total words',
  'useFileDetail.paragraphCount': 'Paragraphs',
  'useFileDetail.sections': 'Sections',
  'useFileDetail.views': 'Views',
  'useFileDetail.times': 'Times',
  'useFileDetail.imgTitle': 'Image title',
  'RenameModal.edit': 'Edit',
  'RenameModal.editSuccess': 'Edit successful',
  'RenameModal.editError': 'Edit failed',
  'RenameModal.title': 'Rename file',
  'RenameModal.InputPlaceholder': 'Please enter file name',
  'RenameModal.validatorMessage': 'Filename must not contain the following invalid characters',
  'SaveTemplate.title': 'Save as a template',
  'SaveTemplate.InputPlaceholder': 'Please enter a name for the template',
  'SaveTemplate.success': 'The template is saved successfully. View it in the "Template Library" - "My Templates".',
  'SaveTemplate.maxlength': 'Theme names can not be more than 10 words',

  'formatTime.justNow': 'Just now',
  'formatTime.minutesAgo': '{minutes} minutes ago',
  'formatTime.today': 'Today {hhmm}',
  'formatTime.yesterday': 'Yesterday {hhmm}',
  'File.setFilter': 'Set filter',
  'File.clearTrash': 'Empty Trash',
  'File.clearTrashSuccess': 'Trash emptied successfully',
  'File.clearTrashError': 'Failed to empty trash',
  'File.clearTrashWarn': 'Are you sure you want to empty the trash? This action cannot be undone',
  'File.clearTrashTips': 'Note: Files in Trash are corporate assets and may be recovered',
  'File.resetFirst': 'File has been restored to its original location',
  'File.resetError': 'Restore failed',
  'File.checkedTotal': '{checked} files selected',
  'File.fileName': 'File name',
  'File.createName': 'Creator',
  'File.updateTime': 'Last updated',
  'File.updatedAt': 'Updated at',
  'File.openTime': 'Opened at',
  'File.editTime': 'Edited at',
  'File.createdAt': 'Created at',
  'File.fileSize': 'Size',
  'File.uncheck': 'Unselect',
  'File.allCheck': 'Select all',
  'File.move': 'Move',
  'File.delete': 'Delete',
  'File.more': 'More',
  'File.deleteSuccess': 'Deleted successfully',
  'File.deleteError': 'Delete failed',
  'File.deleteTips': 'Batch delete limit: {max} files',
  'File.newTabOpens': 'Open in new tab',
  'File.edit': 'Edit',
  'File.star': 'Add to favorites',
  'File.starSuccess': 'Added to favorites successfully',
  'File.starError': 'Failed to add to favorites',
  'File.removeStar': 'Remove from favorites',
  'File.removeSuccess': 'Removed from favorites successfully',
  'File.removeError': 'Failed to remove from favorites',
  'File.share': 'Share',
  'File.view': 'Preview',
  'File.shareInfo': 'Sharing info',
  'File.collaboration': 'Collaborate',
  'File.download': 'Download',
  'File.downloadSuccess': 'Download successful',
  'File.downloadError': 'Download failed',
  'File.png': 'Image',
  'File.imagePdf': 'Pure image PDF',
  'File.reName': 'Rename',
  'File.moveTo': 'Move to',
  'File.copyTo': 'Create a copy to',
  'File.copy': 'Create a copy',
  'File.exitShare': 'Exit share',
  'File.exitShareSure': 'Sure to exit',
  'File.createACopyHere': 'Create a copy here',
  'File.exitSuccess': 'Exit successful',
  'File.clearLastRecord': 'Clear last record',
  'File.clearRecord': 'Clear this record',
  'File.clearFilter': 'Clear filter',
  'File.recentlyOpened': 'Recently opened',
  'File.recentlyEdit': 'Recently edited',
  'File.anyCreator': 'No creator restrictions',
  'File.myCreated': 'I created',
  'File.createToHere': 'Create to here',
  // 'File.getDetailFailed': 'Failed to get file details',
  'File.selectPreviewFile': 'Please select the file to preview',
  'File.cloudFileInfo': 'Cloud file information',
  'File.fileInfo': 'File information',
  'File.shortcutInfo': 'Shortcut information',
  'File.selectFileToPreview': 'Please select a file to preview',
  'File.deleteTime': 'Deleted time',
  'File.recover': 'Recover file',
  'File.recordClearSuccess': 'Record cleared successfully',
  'File.deleteCompletely': 'Permanently delete',
  'File.deleteCompletelyContent':
    'This operation will delete this {name} from the Recycle Bin. Once deleted, you will not be able to recover this  {name}. Please proceed with caution',
  'File.noRecentTitle': 'There is no record of any use yet',
  'File.noRecentDescription': 'You can see the files that have been manipulated here',
  'File.noShareTitle': 'No collaboration invites received',
  'File.noShareDescription': "You'll see files and folders you were added to here",
  'File.noFavoritesTitle': 'No favorites',
  'File.noFavoritesDescription': "You'll see your favorited files and folders here",
  'File.noDesktopTitle': 'No files created yet',
  'File.noDesktopDescription': "You'll see your created files here",
  'File.noTrashTitle': 'No files in trash',
  'File.noTrashDescription': 'Deleted files and folders appear here and can be restored',
  'File.noDownloadPermission':
    'Due to enterprise settings, some files are not authorized for download. Please contact the administrator for assistance',
  'File.defaultSort': 'Default sort',
  'File.sortByFileName': 'Sort by file name',
  'File.sortByCreationTime': 'Sort by creation time',
  'File.byNewnessSortBy': 'Sort by update time',
  'File.noRecentlyUsedFiles': 'No recent files',
  'File.folderAtTop': 'Pin folders on top',
  'File.sortByName': 'Sort by name',
  'File.sortByDeletionTime': 'Sort by deletion time',
  'MessageCenter.commented': 'Commented',
  'MessageCenter.mentioned': 'Mentioned you',
  'MessageCenter.addCollaborator': 'Added you as {invitedRole}',
  'MessageCenter.setAdministrator': 'Set you up as an enterprise administrator',
  'MessageCenter.inviteJoinBusiness': 'Invited you to join the company',
  'MessageCenter.newMembersJoin': 'New member joined',
  'MessageCenter.deleteDocument': 'Document deleted',
  'MessageCenter.remindsReviewTasks': 'Reminder to review task',
  'MessageCenter.liked': 'Liked',
  'MessageCenter.NotificationToDoChanges': 'Removed you from',
  'MessageCenter.dateArrived': 'Date has arrived',
  'MessageCenter.mentionYou': 'Mentioned you',
  'MessageCenter.moveYouOfBusiness': 'Removed you from the company',
  'MessageCenter.handingOverBusinessToYou': 'Transferred company to you',
  'MessageCenter.companyNameChanged': 'Changed the company name to “{name}”',
  'MessageCenter.openedBusinessLink': 'Commented and opened the company invite link',
  'MessageCenter.closedBusinessLink': 'Closed the company invite link',
  'MessageCenter.taskReminders': 'Reminded you to view the task',
  'MessageCenter.tableSelectionReminders': 'The focus area has been updated',
  'MessageCenter.changeUserConfig': 'Changed your account information',
  'MessageCenter.systemNotifications': 'System notification',
  'MessageCenter.application': 'Application',
  'MessageCenter.markRead': 'Mark as read',
  'MessageCenter.join': 'Join',
  'MessageCenter.discussion': 'Discussion of',
  'MessageCenter.permissions': 'Permissions',
  'MessageCenter.fileRecovered': 'The files were recovered',
  'MessageCenter.updateFile': 'Updated documentation',
  'MessageCenter.formNotifications': 'Form notifications',
  'MessageCenter.quotaNotifications': 'Quota alert notifications',
  'MessageCenter.memberReminders': 'Member Reminders',
  'MessageCenter.fileAction': 'Notifications of addition, modification, and deletion of files',
  'MessageCenter.permissionReadable': 'Readable',
  'MessageCenter.permissionCommentable': 'Commentable',
  'MessageCenter.permissionEditable': 'Editable',
  'File.downloadErr': 'File download failed, please try again later',
  'File.startDownload': 'Start download',
  'File.startImport': 'File import started',
  'File.isLoading': 'File is loading, please wait',
  'File.importSuccess': 'File imported successfully',
  'File.importErr': 'File import failed',
  'File.importTypeErr': 'Incorrect file import type',
  'File.isLastImg': 'This is the last image',
  'File.noData': 'No data available',
  'File.unknownParentDirectory': 'Unknown parent directory /',
  'File.noPermissionToViewParentDirectory': 'No permission to view parent directory',
  'MessageCenter.basicInformationModification': 'Basic information modified',
  'Management.emailOrMobile': 'Please select email or phone verification',
  'Space.dragSubTip': 'Release to pin to the top',
  'Space.dragTip': 'Drag the frequently used spaces here or click the top-left corner of the space card to pin it',
  'Space.unTop': 'Remove from the top position',
  'Space.top': 'top',
  'Space.countSpace': 'Spaces',
  'Space.createSpace': 'Create new space',
  'Space.createTeamSpace': 'Create new team space',
  'Space.sure': 'Confirm',
  'Space.cancel': 'Cancel',
  'Space.enterSpaceName': 'Please enter space name',
  'Space.SpaceNameHeader': 'Space name',
  'Space.infoSuccess': 'Created successfully',
  'Space.infoEmpty': 'Space name cannot be empty',
  'Space.infoWaring1': 'Leading spaces removed automatically',
  'Space.infoWaring2': 'Space name cannot exceed 20 characters',
  'Space.rightClickShare': 'Share',
  'Space.rightClickCollaboration': 'Collaborate',
  'Space.rightClickSetting': 'Settings',
  'Space.rightClickDelete': 'Delete',
  'Space.infoEditSuccess': 'Successfully modified',
  'Space.teamspaceSetting': 'Team space settings',
  'Space.teamspaceOwnership': 'Ownership',
  'Space.teamspaceWhatOwnership': 'What is ownership?',
  'Space.teamspaceBelongsto': 'This space is owned by',
  'Space.teamspaceOwnershipExp':
    'Ownership determines who manages the files in a team space. If the ownership belongs to the enterprise, the files will be subject to enterprise security settings, recorded by the audit system, and included in performance dashboard statistics.',
  'Space.teamspaceConfirmDeletion': 'Confirm deletion',
  'Space.teamspaceDeleteSuccess': 'Deleted successfully',
  'Space.teamspaceDeleteTipText':
    'Are you sure you want to delete the team space? Once deleted, all collaborators will lose access',
  'Space.noTeamspace': 'No team space available',
  'Space.noTeamspaceTipText': 'Click the "New" button in the top-left corner to create a space for your team',
  'Space.teamspaceDeleteTitleMobile': 'Are you sure you want to delete the team space?',
  'Space.teamspaceDeleteContentMobile': 'Once deleted, all collaborators will lose access',
  'Space.notice': 'Dynamic',
  'Space.noticeTips': 'There is no space dynamic',
  'Space.noticeDescription':
    'Here you can see information about the creation, updating, and commenting of files in this space.',
  'Space.announcement': 'Announcement',
  'Space.spaceAnnouncement': 'Space announcement',
  'Space.clickEditAnnouncement': 'Click here to edit the announcement',
  'Space.announcementMaxTips': 'Fill in the space announcement (within 500 words)',
  'Space.clear': 'Clear',
  'Space.publish': 'Publish',
  'Space.publishOn': 'Publish on',
  'Space.noticeUpdate': 'The document has been updated',
  'Space.noticeComment': 'Commented on the document',
  'Space.noticeDiscuss': 'Discuss on the document',
  'Space.noticeCreate': 'The file was created',
  'Space.noticeMoreBefore': 'Expand the remaining',
  'Space.noticeMoreAffter': 'Stories',
  'Space.totalNotice': 'There are {total} updates',

  'Profile.title': 'Profile settings',
  'Profile.accountInfo': 'Account information',
  'Profile.preferenceSitting': 'Preferences',
  'Profile.accountID': 'Account ID',
  'Profile.modifyInfo': 'Edit basic information',
  'Profile.safetySetting': 'Security settings',
  'Profile.accountPd': 'Password',
  'Profile.modify': 'Edit',
  'Profile.modifyImg': 'Change avatar',
  'Profile.uploadImg': 'Upload avatar',
  'Profile.nickName': 'Nickname',
  'Profile.enterNickname': 'Please enter a nickname',
  'Profile.nickNameRule': 'Nickname must not exceed 20 characters',
  'Profile.modifySuccess': 'User information updated successfully',
  'Profile.modifyFailed': 'Failed to update user information',
  'Profile.getUploadToken': 'Failed to obtain upload token',
  'Profile.uploadImgSuccess': 'Image uploaded successfully',
  'Profile.uploadImgFailed': 'Failed to upload image',
  'Profile.changePd': 'Change password',
  'Profile.forgetPd': 'Forgot password',
  'Profile.currentPassword': 'Current password',
  'Profile.newPassword': 'Set password',
  'Profile.confirmNewPassword': 'Confirm new password',
  'Profile.currentPasswordRequired': 'Current password is required',
  'Profile.currentPasswordPlaceholder': 'Please enter current password',
  'Profile.newPasswordPlaceholder': 'Please enter new password',
  'Profile.newPasswordRequired': 'New password is required',
  'Profile.newPasswordLength8': 'Password must be at least 8 characters',
  'Profile.newPasswordRule': 'Password must contain numbers, uppercase and lowercase letters',
  'Profile.confirmNewPasswordPlaceholder': 'Please re-enter new password',
  'Profile.confirmNewPasswordRequired': 'Please confirm new password',
  'Profile.confirmNewPasswordMatch': 'Passwords do not match',
  'Profile.changePdSuccess': 'Password changed successfully',
  'Profile.changePdFailed': 'Incorrect password entered',
  'Profile.uploadImgRuleType': 'Only JPG, PNG, GIF, and JPEG formats are supported',
  'Profile.uploadImgRuleSize': 'Image size must not exceed 2MB',
  'Profile.networkError': 'Network response error',
  'ManagementSiderMenu.backDesktopTest': 'Return to homepage',
  'FileMenuPopover.favorite': 'Favorite',
  'FileMenuPopover.move': 'Move',
  'FileMenuPopover.createCopy': 'Create copy',
  'FileMenuPopover.download': 'Download',
  'FileMenuPopover.print': 'Print',
  'FileMenuPopover.saveVersion': 'Save version',
  'FileMenuPopover.viewHistory': 'View history',
  'FileMenuPopover.viewCommentList': 'View comment list',
  'FileMenuPopover.help': 'Help',
  'FileMenuPopover.guide': 'User Guide',
  'FileMenuPopover.shortcut': 'Shortcut keys',
  'FileMenuPopover.helpCenter': 'Help Center',
  'FileMenuPopover.delete': 'Delete',
  'FileMenuPopover.downImage': 'Image',
  'FileMenuPopover.downWord': 'Word',
  'FileMenuPopover.downPDF': 'PDF',
  'FileMenuPopover.downMarkdown': 'Markdown',
  'FileMenuPopover.downWPS': 'WPS',
  'FileMenuPopover.downImagePDF': 'Image-only PDF',
  'FileMenuPopover.downExcel': 'Excel',
  'FileMenuPopover.downZip': 'ZIP',
  'FileMenuPopover.downPPTX': 'PPTX',
  'FileMenuPopover.convertToMoSheet': 'Convert to Shimo Sheet',
  'FileMenuPopover.downToExcel': 'Download as Excel',
  'FileMenuPopover.tableHelp': 'Table help center',
  'FileMenuPopover.addComment': 'Add comment',
  'FileMenuPopover.viewComment': 'View comments',
  'FileMenuPopover.formHelp': 'Form Help Center',
  'FileMenuPopover.documentInfo': 'Document info',
  'FileMenuPopover.whiteboardInfo': 'Whiteboard info',
  'FileMenuPopover.fileInfo': 'File info',
  'FileMenuPopover.noMovePermissionTip':
    "You don't have permission to move the file. Please contact the file owner or admin",
  'FileMenuPopover.noCreateCopyPermissionTip':
    "You don't have permission to create a copy. Please contact the file owner or admin",
  'FileMenuPopover.subscribeUpdate': 'Subscribe to file updates',
  'FileMenuPopover.subscribeUpdatePpt': 'Subscribe to slideshow updates',
  'FileMenuPopover.saveTemplate': 'Save template',
  'FileMenuPopover.show': 'Show/Hide',
  'FileMenuPopover.lockFile': 'Lock file',
  'FileMenuPopover.viewLockMosheet': 'View the locked worksheet',
  'FileMenuPopover.showCommentCards': 'Comment cards are displayed',
  'FileMenuPopover.showMenu': 'Display the table of contents',
  'FileMenuPopover.showWriter': 'Displays the writer',
  'UploadBoard.folderUploadError': 'Folder upload error',
  'UploadBoard.uploadSuccessTitle': 'Upload complete',
  'UploadBoard.uploadingTitle': 'Uploading',
  'UploadBoard.uploadFailTitle': 'Upload failed',
  'UploadBoard.uploadCancelTitle': 'Upload canceled',
  'UploadBoard.uploadConfirmCancelTitle': 'Cancel upload?',
  'UploadBoard.uploadConfirmCancelContent':
    "Closing this panel will cancel ongoing uploads. Uploaded files won't be affected. Continue?",
  'UploadBoard.uploadConfirmCancelOkText': 'Cancel upload',
  'UploadBoard.uploadConfirmCancelCancelText': 'Continue upload',
  'UploadBoard.uploadFailTipTitleText': 'Some files failed to upload. Please retry or copy the error info for support:',
  'UploadBoard.uploadFailMessageText': 'Error message',
  'UploadBoard.uploadCopyFailMessageText': 'Copy error info',
  'UploadBoard.uploadCopySuccessText': 'Copied successfully',
  'UploadBoard.uploadCheckFailMessageText': 'View error info',
  'UploadBoard.uploadRetryText': 'Retry',
  'UploadBoard.uploadOpenFolderText': 'Open folder',
  'UploadBoard.uploadStatusTipCancelText': 'Canceled',
  'UploadBoard.uploadExpTipRetractText': 'Collapse',
  'UploadBoard.uploadExpTipExpandText': 'Expand',
  'UploadBoard.uploadExpTipRetractErrorMessageText': 'Hide error details',
  'UploadBoard.failTipNum': '{number} file(s)',
  'UploadBoard.failTipTitle':
    'Unknown error detected. Please upload these {number} file(s) separately or contact support:',
  'UseFileUpload.networkError': 'Network error',
  'UseFileUpload.noSpaceTitle': 'Storage full',
  'UseFileUpload.noSpaceContent': 'Your storage is full. Please contact support or sales to upgrade your plan',
  'UseFileUpload.noSpaceOkText': 'OK',
  'UseFileUpload.limit80Files': 'Up to 80 files can be uploaded at once',
  'UseFileUpload.limit1GFiles': 'The size of a single uploaded file should not exceed 1G',
  'UseFileUpload.limitSensitiveWords':
    'The file title contains sensitive or illegal words, please check and modify the title content.',
  'compressImage.noCanvas': 'Unable to obtain canvas context',
  'compressImage.pictureTransformError': 'Image conversion failed',
  'compressImage.pictureLoadError': 'Image loading failed',
  'compressImage.pictureReadError': 'Fail to read file',
  'DownloadBoard.downloadWarnErrorText': 'Partial file packaging failed',
  'DownloadBoard.downloadSuccessTitle': 'Packaging completed',
  'DownloadBoard.downloadingTitle': 'Packaging in progress',
  'DownloadBoard.downloadFailTitle': 'Packaging error occurred',
  'DownloadBoard.downloadConfirmCancelTitle': 'Are you sure to cancel the download task?',
  'DownloadBoard.downloadConfirmCancelContent':
    'You have a task that is currently being downloaded. Closing the list will result in the task being cancelled. Are you sure you want to cancel the download?',
  'DownloadBoard.downloadConfirmCancelOkText': 'Cancel download task',
  'DownloadBoard.downloadConfirmDeleteText': 'Delete download task',
  'DownloadBoard.downloadConfirmCancelCancelText': 'Continue downloading tasks',
  'DownloadBoard.downloadFailTipTitleText':
    'Partial file packaging failed, please download or copy the following error message again and submit it to customer service for processing:',
  'useFileDownload.limitPackageCount500': 'The maximum number of downloaded files per download is 500',
  'useFileDownload.limitPackageSize20G': 'A single download file must not exceed 20GB',
  'Management.backDesktop': 'Back to dashboard',
  'Management.enterpriseManagementSystem': 'Enterprise management',
  'Management.businessID': 'Enterprise ID',
  'Management.companyName': 'Company name',
  'Management.workingDays': 'Workdays',
  'Management.performance': 'Performance dashboard',
  'Management.board': 'Dashboard',
  'Management.memberList': 'Contacts',
  'Management.auditLog': 'Audit log',
  'Management.kitValuePack': 'Suite Value Pack',
  'Management.onlineSeatWhiteList': 'Online seat allowlist',
  'Management.settings': 'Settings',
  'Management.template': 'Template settings',
  'Management.daysAndHours': '{days} days {hours} hours',
  'Management.linkEmail': 'Link email',
  'Management.verify': 'Verify',
  'Management.2StepAuthentication': 'Two-step verification',
  'Management.enterVerificationCode': 'Enter code',
  'Management.getVerificationCode': 'Get code',
  'Management.reacquire': 'Retry in {countDownNum}s',
  'Management.unknownError': 'Unknown error',
  'Management.verificationCodeError': 'Email needs {htmlString} before 2FA. Please wait',
  'Management.verificationCodeError2': 'Your email will be eligible for 2FA in',
  'Management.verificationCodeError3': 'Please wait',
  'Management.linkOrVerifyNow': 'Link or verify now',
  'Management.phoneVerification': 'Phone verification',
  'Management.authentication':
    "You haven't linked or verified your email. For enterprise file security, file control features require email linking and a second-step verification.",
  'Management.firstEmailAuthentication': 'If this is your first email link, 2FA will be available after 7x24 hours',
  'Management.endEmailAuthentication': 'After linking your email, a verification code is required for 2FA',
  'Management.verifyPhoneNumber': 'Verify phone number',
  'Management.sMSVerificationCodeSent': 'SMS code sent to',
  'Management.voiceVerificationCodeSent': 'Voice code sent to',
  'Management.resend': 'Resend',
  'Management.getVoiceCode': 'Get voice code',
  'Management.obtainDigitalCode': 'Get numeric code',
  'Management.minutesPleaseTry': 'Valid for 10 minutes. If not received, please try again',
  'Management.sendAgainInSeconds': 'Resend in {voiceCountDownNum}s',
  'Management.noMobile': 'Phone number not linked or verified',
  'Management.linkMobile': 'Link phone number',
  'Management.mobileAuthentication':
    'For corporate security considerations, admin mode requires phone number binding and a second-step verification via SMS code.',
  'Management.emailVerification': 'Email verification',
  'Management.passwordEffect': 'Password activated',
  'Management.enterAdminMode': 'Enter admin mode',
  'Management.managementModePasswordSuccess': 'Admin mode password set successfully',
  'Management.enterAdminModePassword': 'Enter admin mode password',
  'Management.enterPassword': 'Enter password',
  'Management.passwordLength': 'At least 8 characters, including upper/lowercase letters and numbers',
  'Management.newPassword': 'New password',
  'Management.inconsistentPasswords': 'Passwords do not match',
  'Management.pleaseEnterPassword': 'Please enter password',
  'Management.pleaseEnterPasswordAgain': 'Please re-enter password',
  'Management.parameterError': 'Parameter error',
  'Management.incorrectVerificationCode': 'Incorrect verification code',
  'Management.internalError': 'Internal error, unable to verify',
  'Management.exitAdminMode': 'Exit management mode',
  'Management.pleaseEnter': 'Please enter',
  'Management.passwordCannotBeEmpty': 'Password cannot be empty',
  'Management.passwordThan8Characters': 'Password must be at least 8 characters',
  'Management.passwordCannotBeLongerThan': 'Password must not exceed 72 characters',
  'Management.pwdContainNCL': 'Password must include numbers, uppercase and lowercase letters',
  'Management.pwdContainsSupported': 'Password contains unsupported special characters',
  'Members.teamId': 'Organization ID {id}',
  'Members.creator': 'Creator',
  'Members.admin': 'Admin',
  'Members.member': 'Member',
  'Members.disabled': 'Disable members',
  'Members.pending': 'Pending invitation',
  'Members.onlyOnRoot': 'Unassigned members',
  'Members.outerSider': 'External collaborator',
  'Members.disableder': 'Disabled member',
  'Members.unactivated': 'Inactive member',
  'Members.fileFormatError': 'Invalid column headers in uploaded file',
  'Members.fileFormatErrorTips': 'Column headers cannot be deleted or modified. Please correct and reupload.',
  'Members.fileEmptyError': 'Uploaded file cannot be empty',
  'Members.tooManyUsers': 'Import exceeds the maximum allowed entries',
  'Members.tooManyUsersTips': 'The maximum number of entries per import is {total}. Please reduce and try again.',
  'Members.openFileError': 'Invalid file format',
  'Members.openFileErrorTips': 'Only .xls and .xlsx formats are supported. Please check and reupload.',
  'Members.partSuccess': 'Some members failed to import',
  'Members.partSuccessTips': 'Download the failed import list to view errors. Fix and reupload.',
  'Members.notEnoughSeat': 'Number of users exceeds available seats',
  'Members.uploadError': 'Upload failed',
  'Members.addSubDepartment': 'Add sub-department',
  'Members.editDepartment': 'Edit department',
  'Members.deleteDepartment': 'Delete department',
  'Members.accountSettings': 'Account settings',
  'Members.editNickName': 'Edit nickname',
  'Members.changeEmail': 'Change email address',
  'Members.bindEmail': 'Bind email',
  'Members.unbindEmail': 'Unbind email',
  'Members.restPassword': 'Reset password',
  'Members.deleteMember': 'Remove member',
  'Members.cancelInvite': 'Cancel invitation',
  'Members.setDepartment': 'Set department',
  'Members.checkCollaboratedFile': 'View enterprise files collaborated by this user',
  'Members.inviteJoinEnterprise': 'Invite this user to join the organization',
  'Members.removeFromAllEnterpriseFiles': 'Remove this user from all organization files',
  'Members.reactivateMember': 'Reactivate member',
  'Members.activateMember': 'Activate member',
  'Members.disableMember': 'Disable member',
  'Members.deleteLog': 'Delete record',
  'Members.addMember': 'Add member',
  'Members.batchAddMember': 'Batch import members',
  'Members.inviteLink': 'Invitation link',
  'Members.searchResults': 'Search results: {results}',
  'Members.subDepartment': 'Sub-department',
  'Members.totalNumber': 'Total members',
  'Members.addMembersSuccess': 'Successfully added {total} people to {name} department',
  'Members.operationSuccess': 'Operation successful',
  'Members.operationFailed': 'Operation failed',
  'Members.unknownError': 'Unknown error',
  'Members.resetPassword': 'Reset password',
  'Members.newPassword': 'New password',
  'Members.submit': 'Submit',
  'Members.cancel': 'Skip for now',
  'Members.currentEmail': 'Current email',
  'Members.emailAddress': 'Email address',
  'Members.newEmail': 'New email',
  'Members.pleaseEnterEmail': 'Please enter email address',
  'Members.emailFormatError': 'Email format error',
  'Members.departmentOperation': 'Department operation',
  'Members.membersAndSubDepartments': 'Members/Sub-departments',
  'Members.email': 'Email',
  'Members.notBoundEmail': 'Not bound email',
  'Members.operation': 'Operation',
  'Members.noData': 'No data',
  'Members.maxAddLimit': 'Maximum 100 people can be added at a time, please add multiple times',
  'Members.addNewMember': 'Add new member',
  'Members.selectEnterpriseMembers': 'Please select enterprise members to add',
  'Members.noSearchResult': 'No search result',
  'Members.cannotDeleteDepartment': 'Cannot delete department',
  'Members.removeSubDepartmentMembersFirst':
    'Please remove members from the department and its sub-departments before deletion.',
  'Members.confirmDisable': 'Confirm disable',
  'Members.reactivate': 'Reactivate',
  'Members.reminder': 'Reminder',
  'Members.ok': 'Ok',
  'Members.inviteLinkClosed': 'Invitation link closed',
  'Members.enableInviteLinkFailed': 'Failed to enable invitation link',
  'Members.disableInviteLinkFailed': 'Failed to disable invitation link',
  'Members.copySuccess': 'Copy successful!',
  'Members.copyFailed': 'Copy failed!',
  'Members.inviteLinkInfo': 'People who receive the invitation link can join the enterprise',
  'Members.linkEnabled': 'Link enabled',
  'Members.copyLink': 'Copy link',
  'Members.confirmCancel': 'Confirm cancel',
  'Members.inviteToEnterprise': 'Invite to join enterprise',
  'Members.confirmInvite': 'Confirm invite',
  'Members.viewEnterpriseFiles': 'View enterprise files that the user is collaborating on',
  'Members.removeCollaboration': 'Cancel collaboration',
  'Members.confirmDelete': 'Confirm delete',
  'Members.deleteBindEmail': 'Delete bound email',
  'Members.deleteEmailWarning':
    'After deleting the binding, the original account login method through email will be invalid. Are you sure to delete?',
  'Members.addDepartmentMembers': 'Add department members',
  'Members.selectMembersToSet': 'Please select members to set',
  'Members.selectMembersToRemove': 'Please select members to remove',
  'Members.removeFromDepartment': 'Remove from department',
  'Members.addMembers': 'Add members',
  'Members.batchImportMembers': 'Batch import members',
  'Members.memberRemovalSuccess': 'Successfully removed {successLen} members',
  'Members.memberRemovalFailed': 'Member removal failed, each member must belong to at least one department',
  'Members.selectedMembers': 'Selected {count} members',
  'Members.addSuccess': 'Added successfully',
  'Members.copy': 'Copy',
  'Members.me': 'Me',
  'Members.unlinkedDingTalkMembers': 'Unlinked DingTalk members',
  'Members.unlinkedWeChatMembers': 'Unlinked WeChat Work members',
  'Members.externalMembers': 'External enterprise members',
  'Members.unlinkedDingTalkTooltip':
    'There are members in the address book who are not associated with the current DingTalk enterprise',
  'Members.unlinkedWeChatTooltip':
    'There are members in the address book who are not associated with the current WeChat Work',
  'Members.externalMembersTooltip': 'There are members in the address book who are not from the current enterprise',
  'Members.learnMore': 'Learn more',
  'Members.memberRemovalSuccessful': 'Successfully removed {successLen} members',
  'Members.partialMemberRemovalFailed':
    'Some members failed to be removed, each member must belong to at least one department',
  'Members.memberRemovalFailedMinDept': 'Member removal failed, each member must belong to at least one department',
  'Members.confirmRemoveMembers': 'Confirm remove {count} members',
  'Members.removeMembersFromDept': 'Remove members from {departmentName}?',
  'Members.confirmRemoveMembersFromDept': 'Confirm to remove {names} from "{departmentName}"?',
  'Members.setActivationRange': 'Set activation range',
  'Members.activateDisableMembers': 'Activate/Disable members',
  'Members.syncFromExternalSystem':
    'Current address book is synchronized from external system, please activate/disable members through "Set activation range".',
  'Members.confirmReactivateMember': 'Confirm to reactivate member "{name}"',
  'Members.confirmActivateMember': 'Confirm to activate member "{name}"',
  'Members.addToDepartment': 'Add to {departmentName}',
  'Members.currentlySelected': 'Currently selected ({count})',
  'Members.verificationEmailSent': 'Verification email has been sent to {email} (new email address), please verify',
  'Members.nickNameModifySuccess': 'User nickname modified successfully',
  'Members.nickNameModifyFailed': 'User nickname modification failed, please try again later!',
  'Members.modifyNickName': 'Modify nickname',
  'Members.currentNickName': 'Current nickname',
  'Members.changeNickName': 'Change nickname',
  'Members.pleaseEnterNickName': 'Please enter nickname first',
  'Members.nickNameMaxLength': 'Nickname cannot exceed 20 characters',
  'Members.nickNameInvalidChars': 'Nickname contains unsupported symbols, please check and re-enter',
  'Members.userNickNamePlaceholder': 'User nickname',
  'Members.noLeadingOrTrailingSpaces': 'No leading or trailing spaces allowed',
  'Members.confirmDeleteDepartment': 'Confirm to delete "{departmentName}"',
  'Members.editDepartmentName': 'Edit department name',
  'Members.departmentName': 'Department name',
  'Members.pleaseEnterDepartmentName': 'Please enter department name first',
  'Members.departmentNameMinLength': 'Department name must be at least one character',
  'Members.departmentNameMaxLength': 'Department name cannot exceed 50 characters',
  'Members.confirmCancelInvite': 'Confirm to cancel invitation "{userName}"',
  'Members.departmentCreateSuccess': '"{departmentName}" created successfully',
  'Members.parentDepartment': 'Parent department',
  'Members.confirmInviteUser': 'Confirm to invite "{userName}" to join the enterprise',
  'Members.dingTalk': 'DingTalk',
  'Members.identityProvider': 'Identity Provider',
  'Members.workWechat': 'WeChat Work',
  'Members.searchMembers': 'Search members',
  'Members.manageInThirdParty': 'Please manage members and departments in {name} address book',
  'Members.userJoinedOtherEnterprise': 'The user has already joined another enterprise and cannot be invited',
  'Members.resignation': 'Resignation',
  'Members.file': 'File',
  'Members.handover': '{type} handover',
  'Members.manageHandover': 'Manage {type} handover',
  'Members.initiateHandover': 'Initiate {type} handover',
  'Members.viewHistoricalHandoverFiles': 'View historical handover files',
  'Members.addFailed': 'Add failed',
  'Members.selectDepartment': 'Select department',
  'Members.membersWillBelongToDepartments': '{count} members will belong to the following departments',
  'Members.clear': 'Clear',
  'Members.confirmRemove': 'Confirm removal',
  'Members.aboutToRemove': 'About to remove {name}',
  'Members.wayOneDisableAccount':
    'Way one: The account will be disabled, and the user will not be able to continue to log in and use the account.',
  'Members.wayTwoRemoveFromEnterprise':
    'Way two: The account will be removed from the enterprise, and the user will continue to log in and use the account as an individual.',
  'Members.wayTwoWarning':
    "This method will retain the user's collaboration or management rights to enterprise files, please proceed with caution.",
  'Members.initialPasswordText': "{name} ({email})'s initial password is {password}",
  'Members.continueAddNext': 'Continue to add the next one',
  'Members.complete': 'Complete',
  'Members.accountNotCorrect': 'Account is incorrect, please re-enter',
  'Members.partAccountNotCorrect': 'Some accounts are incorrect, please re-enter',
  'Members.accountJoinedOtherEnterprise': '{accounts} have already joined other enterprises, invitation failed',
  'Members.enterMemberEmailOrPhone':
    'Enter member email or phone number for quick adding; members will receive invitation emails after adding',
  'Members.batchAdd': 'Batch Add',
  'Members.enterPhoneOrEmailSeparatedByComma': 'Enter phone numbers or emails, separated by commas ","',
  'Members.memberNumber': 'Member {number}',
  'Members.enterMemberEmailOrPhoneRequired': 'Please enter member email or phone number',
  'Members.addTo': 'Add to {departmentName}',
  'Members.enterNameAndEmailForMember':
    'Enter member name and email, members will receive emails with initial passwords after adding.',
  'Members.name': 'Name',
  'Members.enterName': 'Please enter name',
  'Members.enterMemberName': 'Please enter member name',
  'Members.enterMemberEmail': 'Please enter member email',
  'Members.enterCorrectEmail': 'Please enter correct email',
  'Members.addAndContinue': 'Add and Continue',
  'Members.contactsImportTemplate': 'Contacts Import Template.xlsx',
  'Members.importFailedList': 'Import Failed List.xlsx',
  'Members.stepOneDownloadTemplate': 'Step 1: Download contact import template',
  'Members.completeContentByTip': 'Please complete the content according to the tip information in the table',
  'Members.downloadTemplate': 'Download Template',
  'Members.stepTwoUploadFile': 'Step 2: Upload the completed form',
  'Members.dragFileToUpload':
    'You can drag files directly to this area for upload; only xls, xlsx format files are supported',
  'Members.selectUploadFile': 'Select Upload File',
  'Members.reselect': 'Reselect',
  'Members.startUpload': 'Start Upload',
  'Members.uploading': 'Uploading',
  'Members.importedSuccessfully': 'Successfully imported {successTotal} people',
  'Members.importAgain': 'Import Again',
  'Members.reupload': 'Re-upload',
  'Members.downloadImportFailedList': 'Download Import Failed List',
  'Members.importSuccessfully': 'Imported Successfully',
  'Members.downloadImportResult': 'Download Import Result',
  'Members.viewParticipatedEnterpriseFiles': 'View enterprise files participated in collaboration',
  'Members.participatedEnterpriseFiles': 'Enterprise files that {userName} participates in collaboration',
  'Members.cancelCollaboration': 'Cancel Collaboration',
  'FilePathPicker.createTitle': 'Select location to create',
  'FilePathPicker.moveTitle': 'Select location to move',
  'FilePathPicker.selectLocation': 'Select a location',
  'FilePathPicker.fileName': 'File name',
  'FilePathPicker.createIn': 'Create in',
  'FilePathPicker.moveTo': 'Move to',
  'FilePathPicker.move': 'Move',
  'FilePathPicker.copy': 'Copy',
  'FilePathPicker.originalName': 'Original file name',
  'FilePathPicker.selectPlaceholder': 'Please select',
  'FilePathPicker.createFolder': 'New folder',
  'FilePathPicker.moveFailed': 'Move failed',
  'FilePathPicker.noMoveToTargetLocationTip': 'Move failed. No permission to move to the target location',
  'FilePathPicker.noMoveFilePermissionTip': 'No permission to move this file. Only the file owner can move it',
  'FilePathPicker.targetLocationExistFile': 'The file already exists in the target folder',
  'FilePathPicker.noSupportTip': 'Folder creation not supported in this location',
  'FilePathPicker.files': 'Files',
  'FilePathPicker.folders': 'Folders',
  'FilePathPicker.noPermissionTip': 'No edit permission in this folder. Cannot create {type}. Contact the folder admin',
  'FilePathPicker.createPlaceHolder': 'Name the folder to be created in “{folderName}”',
  'FilePathPicker.createFolderSuccess': 'Folder created successfully',
  'FilePathPicker.createFolderFailed': 'Failed to create folder',
  'FilePathPicker.cancel': 'Cancel',
  'FilePathPicker.confirm': 'Confirm',
  'FilePathPicker.createCopy': 'Create a copy',
  'FilePathPicker.quickEntry': 'Quick access',
  'FilePathPicker.desktopSpace': 'Desktop & Space',
  'FilePathPicker.recent': 'Recent locations',
  'FilePathPicker.shared': 'Shared with me',
  'FilePathPicker.favorites': 'My favorites',
  'FilePathPicker.desktop': 'My desktop',
  'FilePathPicker.space': 'Team space',
  'FilePathPicker.createSuccess': 'Created successfully. Copy saved to “{folderName}”',
  'FilePathPicker.moveSuccess': 'Moved successfully to “{folderName}”',
  'FilePathPicker.emptyUsedTitle': 'No files moved yet',
  'FilePathPicker.emptyUsedSubTitle': 'You can see the folders you recently moved files to here',
  'FilePathPicker.emptySharedTitle': 'No folders shared with you yet',
  'FilePathPicker.emptySharedSubTitle': 'This is where folders shared with you as a collaborator appear',
  'FilePathPicker.emptyFavoritesTitle': 'No favorite folders yet',
  'FilePathPicker.emptyFavoritesSubTitle': 'Your favorite folders will appear here',
  'FilePathPicker.emptySpaceTitle': 'No team spaces yet',
  'FilePathPicker.emptySpaceSubTitle': 'Team spaces you can edit will appear here',
  'FilePathPicker.emptyFolderTitle': 'No folders created yet',
  'FilePathPicker.emptyFolderSubTitle': 'Folders in “{folderName}” will appear here',
  'FilePathPicker.noSearchResult': 'No results found',
  'FilePathPicker.searchPlaceholder': 'Enter keywords',
  'FilePathPicker.selectFolder': 'Select folder',
  'FilePathPicker.noFolder': 'No folders in this location',
  'FilePathPicker.moveHere': 'Move here',

  'AI.popContent': 'I am your intelligent office assistant. Click on the icon and come chat with me!',
  'AI.popTitle': 'Welcome to use intelligent AI',
  'AI.aiName': 'Intelligent AI',
  'AI.welcomeText':
    'Hello, I’m your intelligent assistant~\n\nHere, you can:\n• Share your thoughts, and I will listen carefully\n• Ask any questions, and I will answer patiently\n• Seek creative inspiration, I can stimulate your imagination\n• Need help with decisions, I can provide multi-perspective advice\nWhat problem do you need my help with today?',

  'TemplateReview.useTemp': 'Use this template',
  'TemplateReview.back': 'Back',
  'TempLatePicker.enterpriseTemp': 'Enterprise template',
  'TempLatePicker.myTemp': 'My template',
  'TempLatePicker.grapTempLibrary': 'Graphite template library',
  'TempLatePicker.reNameTemp': 'Rename template',
  'TempLatePicker.pleEeNameTemp': 'Please enter template name',
  'TempLatePicker.deleteContent': 'Template cannot be restored after deletion. are you sure to delete it?',
  'TempLatePicker.areCreating': 'Are creating',
  'TempLatePicker.tempLibrary': 'Template library',
  'TemplateReview.inTopRightFile': 'In the top right corner of the file editing page',
  'TemplateReview.clickSaveTemp': 'Click「...」-「saved as a template」the template can be generated',
  'TemplateReview.notUploadedTemp': 'Enterprise has not uploaded template yet',
  'TemplateReview.pleaseEnCreation': 'Please contact enterprise creation',
  'TemplateReview.afterTheTempRestored': 'After deleting, the template cannot be restored',
  'TemplateReview.areYouSureDelete': 'Are you sure you want to delete the template「{name}」?',
  'TabContent.preview': 'Preview',
  'TabContent.use': 'Use',
  'TabContent.testFormSubTitle': 'Validate answers / Set scores',
  'TabContent.tableFormSubTitle': 'Bulk fill responses',
  'TabContent.formSubTitle': 'Data collection / Survey',
  'TabContent.pptSubTitle': 'Work report / Business presentation',
  'TabContent.tableSubTitle': 'Task management / Data entry',
  'TabContent.sheetSubTitle': 'Work report / Business presentation',
  'TabContent.docxSubTitle': 'Contracts / Official documents',
  'TabContent.docSubTitle': 'Quick notes / Text editing',
  'TabContent.empty': 'Blank',
  'TabContent.homeSubTitle':
    'You can see deleted files and folders here, which can be restored to their original location',
  'Loading.loading': 'Loading',
  'TrashHeader.enterpriseSetting': 'Organization settings',
  'TrashHeader.enterpriseTrash': 'Organization trash',
  'TrashHeader.enterpriseId': 'Organization ID',
  'ManageEmpty.verifyTip': 'To protect organization files, please verify your identity. Click',
  'ManageEmpty.toVerify': 'Verify now',
  'TrashScreen.filterCriteria': 'Filter criteria',
  'TrashScreen.clearCriteria': 'Clear all',
  'TrashScreen.filter': 'Filter',
  'TrashTable.filterResult': 'Filter results',
  'TrashTable.reset': 'Restore',
  'TrashTable.noData': 'No data available',
  'Trash.recover': 'Recover {name}',
  'RecoverFileModal.successfullyRestored': 'Successfully restored',
  'RecoverFileModal.restoreFiles': 'Restore files',
  'RecoverFileModal.ok': 'OK',
  'RecoverFileModal.openBlank': 'Open file in a new tab',
  'RecoverFileModal.spaceReset': 'Select space to restore {length} files',
  'RecoverFileModal.copyAllLink': 'Copy all links',
  'RecoverFileModal.selectSpaceReset': 'Selected {length} files will be restored to a team space',
  'FileItem.copyLink': 'Copy link',
  'DebounceSelect.noData': 'No data available',
  'FileTypeSelect.noSelectType': 'No file type selected',
  'FileTypeSelect.fileType': 'File types',
  'useRecoverFile.resetSuccess': 'Restored successfully',
  'useRecoverFile.spaceError': 'Failed to retrieve space',
  'useScreen.searchFile': 'Search files',
  'useScreen.searchFilePlaceholder': 'Enter title, link, or GUID',
  'useScreen.fileType': 'File type',
  'useScreen.searchDeleteUser': 'Search by deleter',
  'useScreen.searchUserPlaceholder': 'Enter name or email',
  'useScreen.searchCreateUser': 'Search by creator',
  'useScreen.deleteTime': 'Deletion time',
  'useTable.refreshError': 'Unknown error. Please refresh and try again',
  'useTable.usePeriodExpired': 'Access expired',
  'useTable.reVerify': 'Please reverify',
  'useTable.doSuccess': 'Action successful',
  'useTable.modalDeleteTitle': 'Permanently delete file',
  'useTable.modalDeleteContent': 'This action will permanently delete the file and cannot be undone',
  'useTable.fileName': 'File name',
  'useTable.selectFileName': 'File name ({length} selected)',
  'useTable.creator': 'Creator',
  'useTable.deleteUser': 'Deleted by',
  'copyHandle.success': 'Copied successfully',
  'copyHandle.error': 'Copy failed',
  'copyHandle.errorForCopy': 'Copy failed. Please copy manually',
  'Time.expiredDay': '(Expired {day} days ago)',
  'Time.dayExpired': '(Expires in {day} days)',
  'Time.hour': 'Hour',
  'Time.mine': 'Minute',
  'Time.second': 'Second',
  'ShareCollaboration.title': 'Share & collaborate',
  'ShareCollaboration.copySuccess': 'Link copied',
  'ShareCollaboration.copyFail': 'Copy failed',
  'ShareCollaboration.back': 'Back',
  'ShareCollaboration.add': 'Add',
  'ShareCollaboration.coauthor': 'Co-author',
  'ShareCollaboration.admin': 'Manager',
  'ShareCollaboration.noCollaborator': 'None',
  'ShareCollaboration.linkShare': 'Link sharing',
  'ShareCollaboration.shareMethod': 'Sharing method',
  'ShareCollaboration.qrCodeShare': 'Share via QR code',
  'ShareCollaboration.copyLink': 'Copy link',
  'ShareCollaboration.accessPassword': 'Access password',
  'ShareCollaboration.setPermission': 'Set permission',
  'ShareCollaboration.linkReadOnly': 'Anyone with the link can view',
  'ShareCollaboration.linkInCompany': 'People in your organization with the link',
  'ShareCollaboration.readOnly': 'View only',
  'ShareCollaboration.comment': 'Can comment',
  'ShareCollaboration.commentAndEdit': 'Can comment and edit',
  'ShareCollaboration.linkInInternet': 'Anyone on the internet with the link',
  'ShareCollaboration.linkInCompanyWithPassword': 'People in your organization with the link and password',
  'ShareCollaboration.linkInternetWithPassword': 'Anyone on the internet with the link and password',
  'ShareCollaboration.day': 'day',
  'ShareCollaboration.searchAddCollab': 'Click here to search and add collaborators',
  'ShareCollaboration.open': 'Enabled',
  'ShareCollaboration.close': 'Disabled; collaborators and managers still have access',
  'ShareCollaboration.linkWithPassword': 'with password',
  'ShareCollaboration.linkPassword': 'Link password',
  'ShareCollaboration.changePassword': 'Change password',
  'ShareCollaboration.needPassword': 'Password required',
  'ShareCollaboration.linkExpiration': 'Link expiration',
  'ShareCollaboration.switchOff': 'Switch off: Link will remain active',
  'ShareCollaboration.switchOn': 'Switch on: Link expires after the set period',
  'ShareCollaboration.expirationClose': 'Expiration off, link remains active',
  'ShareCollaboration.remaining': 'Remaining',
  'ShareCollaboration.inheritPermission': 'Inherit permission',
  'ShareCollaboration.forbidAccess': 'Access forbidden',
  'ShareCollaboration.removePermission': 'Remove permission',
  'ShareCollaboration.modifySuccess': 'Modified successfully',
  'ShareCollaboration.deleteSuccess': 'Deleted successfully',
  'ShareCollaboration.addCoauthor': 'Add collaborator',
  'ShareCollaboration.onlyManagerCanAddCoauthor': 'Only managers can add collaborators',
  'ShareCollaboration.noPermission': 'No collaboration permission',
  'ShareCollaboration.onlyManagerCanAddManager': 'Only managers can assign manager roles',
  'ShareCollaboration.parentCoauthor': 'Parent folder collaborator',
  'ShareCollaboration.collapse': 'Collapse',
  'ShareCollaboration.expand': 'Expand',
  'ShareCollaboration.addManager': 'Add manager',
  'ShareCollaboration.removeManager': 'Remove manager permissions',
  'ShareCollaboration.removeManagerSuccess': 'Department manager permissions removed successfully',
  'ShareCollaboration.removeManagerSuccess2': 'User manager permissions removed successfully',
  'ShareCollaboration.addSuccess': 'Added successfully',
  'ShareCollaboration.removeSuccess': 'Manager permissions removed successfully',
  'ShareCollaboration.setManager': 'Set as manager',
  'ShareCollaboration.addPermission': 'Add permission',
  'ShareCollaboration.deleteDepartmentSuccess': 'Department deleted successfully',
  'ShareCollaboration.deleteUserSuccess': 'User deleted successfully',
  'ShareCollaboration.operationSuccess': 'Operation successful',
  'ShareCollaboration.recent': 'Recent contacts',
  'ShareCollaboration.organization': 'Organization structure',
  'ShareCollaboration.clickHereToSearchAndAdd': 'Click here to search and add',
  'ShareCollaboration.searchResult': 'Search results',
  'ShareCollaboration.sendNotificationToTheOther': 'Send notification when adding collaborator/manager',
  'ShareCollaboration.notSupportShare': 'Cloud files/folders/workspaces cannot be publicly shared',
  'ShareCollaboration.noPermissionText':
    'According to the enterprise Settings, you do not have the permission to modify the public links. Please contact the file manager or enterprise administrator',
  'ShareCollaboration.noParentCollaborator': '(No parent folder collaborator)',
  'ShareCollaboration.noChildDepartment': '(No sub-departments or members)',
  'ShareCollaboration.confirmRemoveCollaborator': 'Confirm removing manager permissions?',
  'ShareCollaboration.removeCollaborator':
    'After removing manager permissions, {name} will become an "editor collaborator" and appear in the collaborator list',
  'ShareCollaboration.confirmRemove': 'Confirm removal',
  'ShareCollaboration.cancel': 'Cancel',
  'ShareCollaboration.success': 'Removed successfully',
  'ShareCollaboration.failed': 'Removal failed',
  'ShareCollaboration.noPermissionCollaboration': 'You do not have permission to exit collaboration',
  'ShareCollaboration.noRoles': 'No collaborators yet',
  'ShareCollaboration.addRoles': 'Add Collaborators/Administrators',
  'ShareCollaboration.addFromContacts': 'Add from Internal Contacts',
  'ShareCollaboration.shareByLink': 'Invite via Link',
  'ShareCollaboration.administrators': 'Administrators',
  'ShareCollaboration.collaborators': 'Collaborators',
  'ShareCollaboration.administrator': 'Administrator',
  'ShareCollaboration.collaborator': 'Collaborator',
  'ShareCollaboration.noCollaborators': 'No collaborators yet',
  'ShareCollaboration.noDepartment': '(There are no subordinate departments or members)',
  'ShareCollaborationMobile.close': 'Close',
  'ShareCollaborationMobile.searchPlaceholder': 'Enter name/email/phone/department to search collaborators',
  'ShareCollaborationMobile.noSearchResult': 'No relevant members or departments found',
  'ShareCollaborationMobile.adminTitle': 'Administrator',
  'ShareCollaborationMobile.adminDescription':
    'List of members participating in file management. Administrators can move and delete files. Inherited administrators from parent directories cannot be removed',
  'ShareCollaborationMobile.collaboratorTitle': 'Collaborator',
  'ShareCollaborationMobile.collaboratorDescription':
    'List of members participating in file collaboration. Collaborators cannot move and delete files',
  'ShareCollaborationMobile.linkShareEnabled': 'Link sharing enabled',
  'ShareCollaborationMobile.linkShareDisabled': 'Link sharing disabled',
  'ShareCollaborationMobile.linkShareDisabledDescription': 'Collaborators and administrators can still access',
  'ShareCollaborationMobile.parentDirectoryCollaborator': 'Parent Directory Collaborator',
  'ShareCollaborationMobile.clickToViewCollaborators': 'Click to view collaborators',
  'ShareCollaborationMobile.copyLink': 'Copy Link',
  'ShareCollaborationMobile.copyLinkAndPassword': 'Copy Link and Password',
  'ShareCollaborationMobile.recentContacts': 'Recent Contacts',
  'ShareCollaborationMobile.organizationStructure': 'Organization Structure',
  'ShareCollaborationMobile.sendNotificationWhenAdding': 'Send notification when adding collaborators',
  'ShareCollaborationMobile.shareSettings': 'Share Settings',
  'ShareCollaborationMobile.shareSettingsDescription': 'People within the company with the link can only read',
  'ShareCollaborationMobile.password': 'Password',
  'ShareCollaborationMobile.linkShareEnabledDescription': 'People with the link can join collaboration',
  'ShareCollaborationMobile.linkExpiryPeriod': 'Link Expiry Period',
  'ShareCollaborationMobile.linkPermission': 'Link Permission',
  'ShareCollaborationMobile.setAdminSuccess': 'Set successfully',
  'ShareCollaborationMobile.removeSuccess': 'Removed successfully',
  'ShareCollaborationMobile.addSuccess': 'Added successfully',
  'ShareCollaborationMobile.inviteByLink': 'Invite via Link (Not Enabled)',
  'ShareCollaborationMobile.inviteByLinkDesc': 'Enable to allow people with the link to join collaboration',
  'ShareCollaborationMobile.fileName': '"{fileName}"',
  'ShareCollaborationMobile.loginTip': 'After confirmation, you will join the collaboration as {userName}({userEmail})',
  'ShareCollaborationMobile.login': 'Login to join collaboration',
  'ShareCollaborationMobile.share': 'Share or forward this page to invite others to join collaboration',
  'ShareCollaborationMobile.invite': '{userName} invited you to join collaboration',
  'ShareCollaborationMobile.currentAccount': 'Current account {name}({email}) already has access to this file',
  'ShareCollaborationMobile.confirmJoin': 'Confirm Join',
  'FilePasswordInput.encryptedFileShared': '{name} shared an encrypted file',
  'FilePasswordInput.encryptedFilePasswordTip': 'Enter the password to access the file',
  'FilePasswordInput.encryptedFilePasswordPlaceholder': 'Enter file password',
  'FilePasswordInput.invalidGuidOrType': 'Invalid fileGuid or fileType',
  'FilePasswordInput.confirm': 'Confirm',
  'FilePasswordInput.PasswordRequired': 'Password required',
  'FilePasswordInput.cannotRemoveFileManager':
    'This file manager cannot be removed. Please remove it from the root directory',
  'Request.noAuth': 'Not authenticated. Please log in again.',
  'Request.noFile': 'File not found',
  'Request.notFound': 'Page not found',
  'Request.fileDeleted': 'File has been deleted',
  'Request.noAuthorization': 'No access permission',
  'Request.forbidden': 'Insufficient permissions',
  'Request.netError': 'Network error',
  'Request.noSeats': 'No available fixed seats',
  'Request.default': 'Request failed, please try again later',
  'Request.userEmailAlready': 'This email is already in use!',
  'Request.notFoundUser': 'User not found',
  'Request.notLastCertificate': 'Cannot unbind the last login credential',
  'Request.parmasError': 'Invalid request parameters',
  'Request.tooManyRequests': 'Too many requests',
  'Request.parameterError': 'Parameter error',
  'Request.noPermission': 'You do not have permission to perform this action',
  'Request.InvalidPassword': 'Incorrect password',
  'Request.templateNotExist': 'Template does not exist',
  'Request.templateReCordExist': 'Duplicate template GUID',
  'Request.insufficientCapacity': 'Insufficient target space or location capacity',
  'Enterprise.settings': 'Organization settings',
  'Enterprise.info': 'Organization information',
  'Enterprise.logo': 'Organization logo',
  'Enterprise.logoTip': 'No logo uploaded yet',
  'Enterprise.modify': 'Edit',
  'Enterprise.upload': 'Upload',
  'Enterprise.toSet': 'Go to settings',
  'Enterprise.view': 'View',
  'Enterprise.name': 'Organization name',
  'Enterprise.id': 'Organization ID',
  'Enterprise.description': 'Organization description',
  'Enterprise.auth': 'Organization license info',
  'Enterprise.authDesc': 'You can view license seats, account status, and validity period in Organization Info',
  'Enterprise.seatNum': 'Seats',
  'Enterprise.seatNumDesc': 'seats (used {userCount})',
  'Enterprise.contactBusiness': 'Contact Sales',
  'Enterprise.accountStatus': 'Account status',
  'Enterprise.accountStatusExpired': 'Expired',
  'Enterprise.accountStatusValid': 'Active',
  'Enterprise.validityPeriod': 'Validity period',
  'Enterprise.fileSettings': 'File settings',
  'Enterprise.capacityManage': 'Storage management',
  'Enterprise.goToSettings': 'Go to settings',
  'Enterprise.templateSettings': 'Template settings',
  'Enterprise.templateManage': 'Template management',
  'Enterprise.template': 'Organization templates',
  'Enterprise.safetySettings': 'Security settings',
  'Enterprise.watermark': 'Display collaborator watermark for the organization',
  'Enterprise.watermarkTip': 'Watermark includes: nickname + email + company name',
  'Enterprise.watermarkTip2': 'Logged-in members will see watermark when accessing organization files',
  'Enterprise.watermarkTip3':
    'Watermarks are not currently displayed for individual users or for non-logged-in users accessing via a public link.',
  'Enterprise.watermarkOn': 'Watermark enabled',
  'Enterprise.watermarkOff': 'Watermark disabled',
  'Enterprise.collaborationWatermark': 'Collaborator watermark settings',
  'Enterprise.collaborationWatermarkDescP1': 'Logged-in members will see watermark when accessing organization files',
  'Enterprise.collaborationWatermarkDescP2':
    'Enable watermark to reduce content leakage risks and hold accountable for information leaks',
  'Enterprise.collaborationWatermarkDescP3':
    'Currently, watermarks are not displayed for individual users or for non-logged-in users accessing via a public link.',
  'Enterprise.collaborationWatermarkChange': 'settings',
  'Enterprise.recycleBin': 'Organization trash',
  'Enterprise.recycleBinDesc': 'Recover or permanently delete files cleared from trash by any member',
  'Enterprise.uploadLogoRule': 'Image size must not exceed 2MB',
  'Enterprise.uploadLogoTip': 'Supports JPG, JPEG, PNG, GIF; max size 2MB',
  'Enterprise.disturbanceManagement': 'Disturbance management',
  'Enterprise.disturbanceTip':
    'Users or departments can add collaboration, @人, and lock the scope of cell permissions to reduce unnecessary notifications and collaboration',
  'Enterprise.enterpriseFileOperationControl': 'Organization-wide file operation control',
  'Enterprise.enterpriseFileOperationControlTip':
    'Control permissions for operations such as collaborator management, download, print, and create copies organization-wide',
  'Enterprise.roleManage': 'Role Management',
  'Enterprise.onlyCreatorCanManageRole': 'Only the creator can manage roles',
  'Enterprise.fileSecurityAlert': 'File Security Alert',
  'Enterprise.fileSecurityAlertDesc1':
    'When you set file-related permissions, you can use this alert switch to set the frequency of file operations for personnel',
  'Enterprise.fileSecurityAlertDesc2':
    'For example: if a single person exports more files than the threshold, an email and notification will be sent:',
  'Enterprise.fileSecurityAlertDesc3': 'Enabling the switch can better prevent enterprise file leakage',
  'Enterprise.goToSet': 'Go to settings',

  'TemplateHeader.enterpriseSetting': 'Organization settings',
  'TemplateHeader.enterpriseTemplateSetting': 'Template settings',
  'TemplateHeader.enterpriseId': 'Organization ID',
  'AddTemplatePop.createSuccess': 'Created successfully',
  'AddTemplatePop.createFailed': 'Failed to create',
  'AddTemplatePop.editSuccess': 'Edited successfully',
  'AddTemplatePop.editFailed': 'Failed to edit',
  'AddTemplatePop.noFileType': 'Unsupported file type',
  'AddTemplatePop.inputSureLink': 'Please enter a valid link',
  'AddTemplatePop.uploadFailed': 'Failed to upload image',
  'AddTemplatePop.tokenFailed': 'Failed to get image upload token',
  'AddTemplatePop.limitJPG': 'Only JPG/PNG images are allowed!',
  'AddTemplatePop.noUp2MB': 'Image size must not exceed 2MB!',
  'AddTemplatePop.inputTemplateName': 'Please enter a template name',
  'AddTemplatePop.selectType': 'Please select a type',
  'AddTemplatePop.autoSelectType': 'Type will be auto-detected from the link',
  'AddTemplatePop.inputLink': 'Please enter a link',
  'AddTemplatePop.uploadImgPlace': 'Please upload an image',
  'AddTemplatePop.templateName': 'Template name',
  'AddTemplatePop.templateType': 'Template type',
  'AddTemplatePop.templateLink': 'Template link',
  'AddTemplatePop.templateImg': 'Template thumbnail',
  'AddTemplatePop.img': 'Thumbnail',
  'AddTemplatePop.uploadImg': 'Upload image',
  'AddTemplatePop.uploadImgRetry': 'Re-upload',
  'AddTemplatePop.createTemplate': 'Create template',
  'AddTemplatePop.editTemplate': 'Edit template',
  'AddTemplatePop.updateTime': 'Last updated',
  'AddTemplatePop.action': 'Actions',
  'AddTemplatePop.edit': 'Edit',
  'AddTemplatePop.delete': 'Delete',
  'TemplateContent.deleteTemplate': 'Delete template',
  'TemplateContent.deleteTemplateContent':
    'Deleting this template will also remove it from the main template library. Are you sure?',
  'TemplateContent.deleteSuccess': 'Deleted successfully',
  'TemplateContent.deleteFail': 'Failed to delete',
  'TemplateContent.create': 'Create template',
  'TemplateContent.createTip': 'No files created yet. Please',
  'TemplateContent.createRightNow': 'Create now',
  'TemplateContent.youCanSeeTemplate': 'You can view your created files here',
  'TemplateContent.noTemplate': 'No template',
  'TemplateContent.upperTempFileAdd': 'youCanAddItThroughTheMenu"SaveAsTemplate"InTheUpperRightCornerOfTheFile',
  'Image.preview': 'Preview',
  'Operation.export': 'Export logs',
  'Operation.noOperationData': 'No operation records',
  'Operation.search': 'Search',
  'Operation.clear': 'Clear filters',
  'Operation.operate': 'Add action',
  'Operation.createdAt': 'Time',
  'Operation.operator': 'Operator',
  'Operation.action': 'Action type',
  'Operation.operand': 'Target',
  'Operation.relate': 'Related account',
  'Operation.ip': 'IP address',
  'Operation.more': 'More',
  'Operation.terminal': 'Terminal',
  'Operation.viewDetail': 'View details',
  'Operation.operateDetail': 'Action details',
  'Operation.addFilter': 'Add filter',
  'Operation.operateClear': 'Clear all',
  'Operation.nape': 'Items',
  'Operation.selectNapeNum': '{num} items selected',
  'Operation.operateSearch': 'Search',
  'Operation.noSearchResult': 'No search results',
  'Operation.selectNapeNumMax': 'Up to {num} items allowed',
  'Operation.dateRange': 'Date range cannot exceed 30 days',
  'Operation.filter.time': 'Time',
  'Operation.filter.operator': 'Operator',
  'Operation.filter.file': 'File',
  'Operation.filter.userEmailTel': 'Username / Email / Phone',
  'Operation.filter.noData': 'No data',
  'Operation.filter.fileUrl': 'File link URL',
  'Operation.filter.operation': 'Action',
  'Operation.exportLoading': 'Exporting, please wait...',
  'Operation.exportSuccess': 'Export successful',
  'Operation.exportError': 'Export failed',
  'Operation.deleted': 'Deleted',

  'Description.collaborators': 'Collaborators',
  'Description.myPermissions': 'My Permissions',
  'Description.type': 'Type',
  'Description.creator': 'Creator',
  'Description.creationTime': 'Creation Time',
  'Description.lastUpdated': 'Last Updated',
  'Description.size': 'Size',
  'Description.openCollaborationPanel': 'Open Collaboration Panel',
  'Description.sizeTooltip':
    'Local files and text, circle, and attachments in undefined files are counted into space capacity.',
  'Description.viewer': 'Viewer',
  'Description.commenter': 'Commenter',
  'Description.editor': 'Editor',
  'Description.owner': 'Owner',
  // 快速访问
  'QuickAccess.label': 'Quick Access',
  'QuickAccess.add': 'Add to Quick Access',
  'QuickAccess.remove': 'Remove from Quick Access',
  'QuickAccess.addSuccess': '「{name}」has been added to Quick Access',
  'QuickAccess.removeSuccess': '「{name}」has been removed from Quick Access',
  'QuickAccess.alreadyExists': '「{name}」is already in Quick Access',
  'QuickAccess.dropToAdd': 'Release to add',
  'QuickAccess.dragToAdd': 'Drag files here for quick access',
  'QuickAccess.multipleFilesNotSupported': 'Multiple files drag to Quick Access is not supported',
  'QuickAccess.fileCount': '{count} files',
  'QuickAccess.limit': 'Quick access list has reached the limit',
  'QuickAccess.modifyRankSuccess': 'Successfully modified the order',

  'My.expireAt': 'Expire',
  'My.accountManagement': 'Account Management',
  'My.nightMode': 'Night Mode',
  'My.interfaceLanguage': 'Interface Language',
  'My.recovery': 'Recycle Bin',
  'My.logout': 'Logout',
  'My.nickName': 'NickName',
  'My.accountID': 'AccountID',
  'My.updateUserName': 'Update UserName',
  'My.updateUserNameSuccess': 'Update Success',
  'My.confirm': 'Confirm',
  'My.nicknameDuplication': 'Nickname Duplication',
  'My.cancel': 'Cancel',
  'My.copyID': 'Copy ID',
  'My.copySuccess': 'Copy Success',
  'My.copyFailed': 'Copy Failed',
  'My.language': 'Language',
  'My.mode': 'Mode',
  'My.nightModeFollowSystem': 'Follow System Setting',
  'My.nightModeDay': 'Day Mode',
  'My.nightModeNight': 'Night Mode',
  'UseFileUploadMobile.upload': 'Upload',
  'UseFileUploadMobile.import': 'Import',
  'UseFileUploadMobile.uploadFailed': 'Upload failed',
  'UseFileUploadMobile.importFailed': 'Import failed',
  'UseFileUploadMobile.uploading': 'Uploading',
  'UseFileUploadMobile.importing': 'Importing',
  'UseFileUploadMobile.uploadSuccess': 'Upload successful',
  'UseFileUploadMobile.importSuccess': 'Import successful',
  'UseFileUploadMobile.clickToView': 'Click to view',
  'UseFileUploadMobile.actionTitle': 'Upload File',
  'UseFileUploadMobile.selectFromManager': 'Select from file manager',
  'UseFileUploadMobile.importFile': 'Import file',
  'UseFileUploadMobile.cancel': 'Cancel',
  'UseFileUploadMobile.uploadList': 'Upload List',
  'UseFileUploadMobile.processing': 'Processing',
  'UseFileUploadMobile.uploadSuccessClickView': 'Upload successful, click to view',
  'UseFileUploadMobile.uploadErrorRetry': 'Upload error, please click to retry',
  'UseFileUploadMobile.uploadCancelled': 'Upload cancelled',
  'UseFileUploadMobile.cancelTaskConfirm': 'Task is not completed yet, are you sure to cancel the task?',
  'UseFileUploadMobile.fail': 'failed',
  'UseFileUploadMobile.uploadFailedClickView': 'Upload failed, click to view "{fileName}"',
  'UseFileUploadMobile.importFailedClickView': 'Import failed, click to view "{fileName}"',
  'UseFileUploadMobile.uploadSuccessClickViewFile': 'Upload successful, click to view "{fileName}"',
  'UseFileUploadMobile.importSuccessClickView': 'Import successful, click to view "{fileName}"',
  'My.themeSwitched': 'Mode has been switched',
  'My.switchLangSuccess': 'Language has been switched',
  'Notification.noMsg': 'No unread messages',
  'Notification.all': 'All notifications',
  'Notification.unread': 'Unread',
  'Notification.readAll': 'Mark all as read',
  'Notification.readonly': 'View only',
  'Notification.comment': 'Can comment',
  'Notification.edit': 'Can edit',
  'Notification.apply': 'Request',
  'Notification.allow': 'Approved',
  'Notification.processed': 'Processed',
  'Notification.processedByAnother': 'Handled by someone else',
  'Notification.noModifyRolePermission': 'No permission to add collaborators',
  'Notification.fileNotFound': 'File not found',
  'Notification.userNotAdmin': 'User is not an admin',
  'Notification.addTeamWorker': 'Added you as a collaborator',
  'Notification.addAdmin': 'Added you as an admin',
  'Notification.hasComment': 'Commented',
  'Notification.many': 'and others',
  'Notification.people': 'people',
  'Notification.likes': 'Liked',
  'Notification.ofComment': 'your comment',
  'Notification.inYou': 'you in',
  'Notification.setReminderHasExpired': 'Your reminder has expired',
  'Notification.modify': 'Modified',
  'Notification.delete': 'Deleted',
  'Notification.youIn': 'mentioned you in',
  'Notification.dateReminder': 'a date reminder',
  'Notification.modifiedYourAccountInfo': 'Updated your account info',
  'Notification.modifiedNickName': 'Changed nickname to',
  'Notification.bindThisEmail': 'Linked this email',
  'Notification.modifiedBindEmail': 'Changed linked email to',
  'Notification.resetPassword': 'Reset password',
  'Notification.setYouAsEnterpriseAdmin': 'Set you as enterprise admin',
  'Notification.modifiedEnterpriseName': 'Updated enterprise name',
  'Notification.mentionYou': 'Mentioned you',
  'Notification.updatedWatchArea': 'Updated watch area',
  'Notification.remindYouToViewTask': 'Reminded you to view the task',
  'RightclickMouse.locateToFolder': 'Locate in folder',
  'RightclickMouse.locateToSpace': 'Locate in the current space',
  'RightclickMouse.createShortcut': 'Create shortcut',
  'RightclickMouse.createShortcutTo': 'Create shortcut to',
  'RightclickMouse.deleteShortcut': 'Delete the shortcut',
  'RightclickMouse.deleteShortcutOnly':
    'Only shortcuts will be deleted. Files and collaboration relationships will not be deleted。',
  'RightclickMouse.deleteShortcutConfirm':
    'Confirm the deletion of the file and the shortcut? After deleting the file, all collaborators will be unable to access it. Deleting the shortcut will not affect it.',
  'RightclickMouse.createdTo': 'Shortcut created in',
  'RightclickMouse.openImmediately': 'Open immediately',
  'RightclickMouse.noPermissionToOpenTheParentDirectory': 'No permission to open parent folder',
  'ApplyForPermissionModal.onlyRead': 'Read Only',
  'ApplyForPermissionModal.onlyComment': 'Comment Only',
  'ApplyForPermissionModal.edit': 'Can Edit',
  'ApplyForPermissionModal.tooManyRequests': 'You are submitting too frequently. Please try again in 2 minutes.',
  'ApplyForPermissionModal.applySuccess': 'Application sent successfully',
  'ApplyForPermissionModal.applyFailed': 'Application failed',
  'ApplyForPermissionModal.applyForPermission': 'Request Access from Admin',
  'ApplyForPermissionModal.selectPermission': 'Select Permission',
  'ApplyForPermissionModal.inputReason': 'Please enter your reason',
  'ApplyForPermissionModal.inputReasonPlaceholder': 'Enter application details',
  'ApplyForPermissionModal.adminWillAcceptApplyMsg': 'The administrator will be notified of your request',
  'ApplyForPermissionModal.apply': 'Send Request',
  'search.searchPlaceholder': 'Search files or folders',
  'search.noRecentFiles': 'No recent files',
  'search.all': 'All',
  'search.folder': 'Folder',
  'search.document': 'Document',
  'search.sheet': 'Sheet',
  'search.appTable': 'App Table',
  'search.slide': 'Slide',
  'search.mindmap': 'Mindmap',
  'search.board': 'Board',
  'search.image': 'Image',
  'search.form': 'Form',
  'search.pdf': 'PDF',
  'search.uploadedFile': 'Uploaded File',
  'search.titleAndContent': 'Title + Content',
  'search.titleOnly': 'Title Only',
  'search.recentlyUsed': 'Recently Used',
  'search.sharedToMe': 'Shared to Me',
  'search.anyTime': 'Any Time',
  'search.lastWeek': 'Last Week',
  'search.lastMonth': 'Last Month',
  'search.lastYear': 'Last Year',
  'search.allLocation': 'All Locations',
  'search.myDesktop': 'My Desktop',
  'search.currentSpace': 'Current Space',
  'search.type': 'File Type',
  'search.range': 'Search Range',
  'search.result': 'Results Display',
  'search.match': 'Match Range',
  'search.timescale': 'Time Range',
  'search.selectCreator': 'Select Creator',
  'search.creatorRange': 'Creator Range',
  'search.loading': 'Loading...',
  'search.reset': 'Reset',
  'search.confirm': 'Confirm',
  'search.today': 'Today',
  'search.yesterday': 'Yesterday',
  'search.edit': 'Edit',
  'search.cancel': 'Cancel',
  'search.searchCreator': 'Search Creator',
  'search.delete': 'Delete',
  'search.oneself': 'oneself',
  'search.creationTime': 'Creation Time',
  'search.startTime': 'Start Time',
  'search.endTime': 'End Time',

  'helpGuide.theme': 'Theme',
  'helpGuide.tool': 'Tool',
  'helpGuide.userGuide': 'User Guide',
  'helpGuide.shortcutKey': 'Shortcuts',
  'helpGuide.documentFeatures': 'Document Features',
  'helpGuide.collaborationFeatures': 'Collaboration Features',
  'helpGuide.textFormat': 'Text Format',
  'helpGuide.insertion': 'Insert',
  'helpGuide.markdownFormat': 'Markdown Format',
  'helpGuide.paragraphFormat': 'Paragraph Format',
  'helpGuide.edit': 'Edit',
  'helpGuide.operation': 'Operations',
  'helpGuide.formatPainter': 'Format Painter',
  'helpGuide.header': 'Header',
  'helpGuide.textStyle': 'Text Style',
  'helpGuide.orderedList': 'Ordered List',
  'helpGuide.unorderedList': 'Ordered/Unordered List',
  'helpGuide.reorderList': 'Renumber Ordered List',
  'helpGuide.taskList': 'Task List',
  'helpGuide.mentionPerson': 'Mention Someone',
  'helpGuide.mentionFile': 'Mention File',
  'helpGuide.insertLocalImage': 'Insert Local Image',
  'helpGuide.searchNetworkImage': 'Search Network Image',
  'helpGuide.insertTable': 'Insert Table',
  'helpGuide.insertAttachment': 'Insert Attachment',
  'helpGuide.insertDateTime': 'Insert Date and Time',
  'helpGuide.insertOnlineMedia': 'Insert Online Media',
  'helpGuide.insertLocalAudio': 'Insert Local Audio',
  'helpGuide.audioToText': 'Audio to Text',
  'helpGuide.insertLocation': 'Insert Location',
  'helpGuide.insertCodeBlock': 'Insert Code Block',
  'helpGuide.insertQuote': 'Insert Quote',
  'helpGuide.findReplace': 'Find and Replace',
  'helpGuide.viewHistory': 'View History',
  'helpGuide.saveVersion': 'Save Version',
  'helpGuide.selectComment': 'Text Selection Comment',
  'helpGuide.export': 'Export',
  'helpGuide.translate': 'Translate',
  'helpGuide.discuss': 'Discussion',
  'helpGuide.share': 'Share',
  'helpGuide.addCollaborator': 'Add Collaborator',
  'helpGuide.bold': 'Bold',
  'helpGuide.italic': 'Italic',
  'helpGuide.underline': 'Underline',
  'helpGuide.strikethrough': 'Strikethrough',
  'helpGuide.highlight': 'Text Highlight',
  'helpGuide.fontSize': 'Font Size',
  'helpGuide.mentionPersonFile': 'Mention Person / File',
  'helpGuide.wordComment': 'Text Selection Comment',
  'helpGuide.hyperlink': 'Hyperlink',
  'helpGuide.location': 'Location',
  'helpGuide.header1': 'Header 1',
  'helpGuide.header2': 'Header 2',
  'helpGuide.header3': 'Header 3',
  'helpGuide.header4': 'Header 4',
  'helpGuide.orderedListText': 'Ordered List',
  'helpGuide.unorderedListText': 'Unordered List',
  'helpGuide.taskListText': 'Task List',
  'helpGuide.quote': 'Quote',
  'helpGuide.codeBlock': 'Code Block',
  'helpGuide.divider': 'Divider',
  'helpGuide.setHeader': 'Set Header',
  'helpGuide.plainText': 'Plain Text',
  'helpGuide.increaseIndent': 'Increase Indent',
  'helpGuide.decreaseIndent': 'Decrease Indent',
  'helpGuide.undo': 'Undo',
  'helpGuide.redo': 'Redo',
  'helpGuide.focusMode': 'Focus Mode',
  'helpGuide.search': 'Search',
  'helpGuide.find': 'Find',
  'helpGuide.findAndReplace': 'Find and Replace',
  'helpGuide.saveVersionContent': 'Save Current Content as Version',
  'helpGuide.toggleHistory': 'Toggle History',
  'helpGuide.toggleDirectory': 'Toggle Directory',
  'helpGuide.enterPresentationMode': 'Enter Presentation Mode',
  'helpGuide.openShimoDeskInNewTab': 'Open Shimo Desktop in New Tab',
  'helpGuide.toggleShortcutPanel': 'Toggle Shortcut Panel',
  'helpGuide.tableFunction': 'Table Function',
  'helpGuide.lightDocumentFunction': 'Light document function',
  'helpGuide.insertModuleCode': 'Insert module code',
  'helpGuide.formatPainterDetail':
    'Quickly copy formatting from one section of text to another. Double-click the "Format Painter" button for continuous use.',
  'helpGuide.headerDetail':
    'Select text, click the "Header" button in the toolbar to apply various header types from the dropdown menu.',
  'helpGuide.textStyleDetail':
    'Select text, click "Font", "Font Size", "Bold", "Italic", "Underline", "Strikethrough" and other buttons in the toolbar to apply various text styles.',
  'helpGuide.unorderedListDetail':
    'Click the "Ordered List" or "Unordered List" button in the toolbar to create ordered or unordered lists.',
  'helpGuide.reorderListDetail':
    'Position cursor at or select an ordered list, click "Ordered List" - "Renumber", enter the starting number and click "Confirm".',
  'helpGuide.taskListDetail':
    'Position cursor at text or select content, click the "Task List" button in toolbar to create a task item. Click the checkbox on the left to mark it as completed.',
  'helpGuide.mentionPersonDetail':
    'Type "@" + name to mention someone. The mentioned person will receive a message notification.',
  'helpGuide.mentionFileDetail':
    'Type "@" + file name to link to documents, tables, or folders. Click to quickly open the mentioned file.',
  'helpGuide.insertLocalImageDetail':
    'Click "Insert" - "Image" - "Upload Local Image" in toolbar to choose and upload images. Select the image to activate the image toolbar, where you can crop or set margins, borders, and alignment.',
  'helpGuide.searchNetworkImageDetail':
    'Click "Insert" - "Image" - "Search Network Image" in toolbar, enter keywords to search for images, and select images to quickly insert into the document.',
  'helpGuide.insertTableDetail':
    'Click "Insert" - "Table" in toolbar to insert a table. Select cells to merge or set text styles in the toolbar.',
  'helpGuide.insertAttachmentDetail': 'Click "Insert" - "Attachment" in toolbar to select and upload files.',
  'helpGuide.insertDateTimeDetail':
    'Click "Insert" - "Date" in toolbar, select date and time in the panel. Check "Remind Me" to receive a notification when the time expires. If no specific time is set, notification will be sent at 8AM on that day.',
  'helpGuide.insertOnlineMediaDetail':
    'Click "Insert" - "Media" - "Online Media" in toolbar, paste media link to embed.',
  'helpGuide.insertLocalAudioDetail':
    'Click "Insert" - "Media" - "Local Audio" in toolbar to select and upload local audio files. After uploading, you can play or download the audio.',
  'helpGuide.audioToTextDetail':
    'Click the "More Actions" button on the local audio player, select "Audio to Text". After conversion, you can insert the text into the document. Currently only supports Chinese conversion.',
  'helpGuide.insertLocationDetail': 'Click "Insert" - "Location" in toolbar to select location in the panel.',
  'helpGuide.insertCodeBlockDetail':
    'Click "Insert" - "Code Block" in toolbar to insert a code block. Supports 176 programming languages including JavaScript, SQL, Java, Python, etc.',
  'helpGuide.insertQuoteDetail': 'Position cursor at text or select content, click "Insert" - "Quote" in toolbar.',
  'helpGuide.findReplaceDetail':
    'Click the "Find and Replace" button to search document content with results highlighted in color. Click the triangle next to "Find" to expand the "Replace" function for replacing search results.',
  'helpGuide.viewHistoryDetail':
    'Click "More" - "View History" in the upper right corner to view all document edit history. Click "Restore" in the upper right corner of history card to restore the document.',
  'helpGuide.saveVersionDetail':
    'Click "More" - "Save Version" in the upper right corner to save current content as a version. Click "More" - "View History" - "Version" to preview or restore saved versions.',
  'helpGuide.selectCommentDetail':
    'Select text or images in the document, click the "Comment" button on the right to add text selection comments.',
  'helpGuide.exportDetail':
    'Click "More" - "Export" in the upper right corner to export the document as PDF, Word, or JPG formats.',
  'helpGuide.translateDetail':
    'Click "Translate" - "Translate Selected" in toolbar, select text in the editing area, and translation results will display in the right panel based on language settings with option to insert at cursor position. Click "Translate" - "Translate Document", set language in the dialog, then click "Translate".',
  'helpGuide.discussDetail':
    'Click "View Discussion" button in the upper right corner to view or participate in discussions',
  'helpGuide.shareDetail':
    'Click "Share" button in the upper right corner, set sharing type and permissions, scan QR code or copy link to share document with others.',
  'helpGuide.addCollaboratorDetail':
    'Click "Collaborate" - "Add Collaborator" in the upper right corner to quickly add collaborators by entering email or searching names. Supports setting three different access permissions for collaborators: "Can Edit", "Can Only Comment", or "Can Only Read".',
  'helpGuide.mindMappingFunction': 'Mind map function',

  'helpGuide.insertSameLevel': 'Insert Same Level Theme',
  'helpGuide.insertSubTheme': 'Insert Sub Theme',
  'helpGuide.insertParentTheme': 'Insert Parent Theme',
  'helpGuide.deleteThemeAndSub': 'Delete Current Theme and Sub-themes',
  'helpGuide.canvas': 'Canvas',
  'helpGuide.canvasZoomIn': 'Canvas Zoom In',
  'helpGuide.canvasZoomOut': 'Canvas Zoom Out',
  'helpGuide.canvasDrag': 'Drag Canvas',
  'helpGuide.themeTextLineBreak': 'Theme Text Line Break',
  'helpGuide.canvasZoom': 'Canvas Zoom',
  'helpGuide.freeThemeDrag': 'Free Theme Drag',
  'helpGuide.themeNavigation': 'Topic navigation',
  'helpGuide.themeNavigationDetail': 'After selecting a theme, use the arrow keys to adjust the selection status.',
  'helpGuide.themeFolding': 'Theme folding',
  'helpGuide.themeFoldingDetail':
    'Click on the "-" behind the topic to collapse and hide all the themes behind the theme.',
  'helpGuide.themeExpansion': 'The topic expands',
  'helpGuide.themeExpansionDetail':
    'Click the "number" behind the topic to restore the first-level topic that is hidden behind the topic to the expanded state.',
  'helpGuide.insertSubThemeDetail': 'Quickly create a new theme after the currently selected theme.',
  'helpGuide.insertSameLevelDetail': 'Quickly create a new theme below the currently selected theme.',
  'helpGuide.insertParentThemeDetail': 'Quickly create a new theme before the currently selected theme.',
  'helpGuide.canvasZoomDetail': 'Click the "+" or "-" buttons in the toolbar to adjust the canvas scale.',
  'helpGuide.freeThemeDragDetail': 'After selecting a theme, press and drag to adjust the theme hierarchy and order.',
  'helpGuide.pointer': 'Pointer',
  'helpGuide.brush': 'Brush',
  'helpGuide.text': 'Text',
  'helpGuide.rectangle': 'Rectangle',
  'helpGuide.circle': 'Circle',
  'helpGuide.line': 'Line',
  'helpGuide.eraser': 'Eraser',
  'helpGuide.zoomPage': 'Zoom Page',
  'helpGuide.currentDate': 'Current Date',
  'helpGuide.currentTime': 'Current Time',
  'helpGuide.currentDateTime': 'Current Date and Time',
  'helpGuide.cancelEdit': 'Cancel Edit',
  'helpGuide.addRow': 'Add Row',
  'helpGuide.cellLineBreak': 'Cell Line Break',
  'helpGuide.areaFilling': 'Area Filling',
  'helpGuide.activateCell': 'Activate Cell',
  'helpGuide.expandSelectionOneCell': 'Expand Selection One Cell',
  'helpGuide.positionToEdge': 'Position to Edge',
  'helpGuide.expandSelectionToTopBottomEdge': 'Expand Selection to Top/Bottom Edge',
  'helpGuide.expandSelectionToLeftRightEdge': 'Expand Selection to Left/Right Edge',
  'helpGuide.fillDown': 'Fill Down',
  'helpGuide.format': 'Format',
  'helpGuide.border': 'Border',
  'helpGuide.backgroundColor': 'Background Color',
  'helpGuide.mergeCells': 'Merge cells',
  'helpGuide.freeze': 'Freeze',
  'helpGuide.formula': 'Formula',
  'helpGuide.insertLink': 'Insert Link',
  'helpGuide.insertImage': 'Insert Image',
  'helpGuide.insertMultipleRowsColumns': 'Insert Multiple Rows/Columns',
  'helpGuide.lockSheetCell': 'Lock Sheet/Cell',
  'helpGuide.hideRowsColumns': 'Hide Rows/Columns',
  'helpGuide.history': 'History',
  'helpGuide.tableFormatDetail':
    'Select a cell, click the "Format" button in the toolbar, then select the format option you want to convert: plain text, number, percentage, scientific notation, currency, date, accounting, etc.',
  'helpGuide.tableTextDetail':
    'Select a cell, click the "Font Size", "Bold", "Italic", "Underline" or "Strikethrough" buttons in the toolbar to set the cell text style.',
  'helpGuide.tableBorderDetail':
    'Select a cell, click the "Border" button to select border position, border color and border style.',
  'helpGuide.tableBackgroundColorDetail': 'Select a cell, click the "Background Color" button, select a color.',
  'helpGuide.tableMergeCellsDetail':
    'Select multiple cells, click the "Merge Cells" button to perform the merge operation.',
  'helpGuide.tableFreezeDetail':
    'Select the row/column you want to freeze, click the freeze button, and select "Freeze to current row/column" in the drop-down menu.',
  'helpGuide.tableFormulaDetail':
    'Click the "Formula" button, select a formula from the drop-down menu, then select cell values for calculation. Switch worksheets to select values for cross-table formula calculations.',
  'helpGuide.tableInsertLinkDetail':
    'Select a cell, click the "Insert Link" button. Move the mouse to a cell with a link to modify or delete the link.',
  'helpGuide.tableInsertImageDetail':
    'Click to upload an image, select upload cell image to upload an image to the selected cell; select upload floating image, the image will float above the cell.',
  'helpGuide.tableFindReplaceDetail':
    'Click the "Find and Replace" button to search the table content, search results are highlighted in color. Click the small triangle to the right of "Find" to replace search results.',
  'helpGuide.tableInsertMultipleRowsColumnsDetail':
    'Select multiple cells, the right-click menu can choose to insert the corresponding number of rows or columns. You can also scroll to the bottom of the page and manually enter the number of rows to insert, up to 1000 rows at a time.',
  'helpGuide.tableLockSheetCellDetail':
    'Select the worksheet or cell you want to lock, select lock from the right-click menu, and you can set "Can edit", "Can only view" or "Forbidden to view" permissions for different collaborators. In "More" - "View locked worksheets", you can view all locked content.',
  'helpGuide.tableHideRowsColumnsDetail':
    'Select the entire row or column you want to hide, select hide selected row or column from the right-click menu.',
  'helpGuide.tableCommentDetail': 'Select a cell, click "Add Comment" to comment on the cell content.',
  'helpGuide.tableHistoryDetail':
    'Click "More" - "View History" to view the table editing history and restore the history. You can also save the current table as a historical version and view it in the history panel.',
  'helpGuide.mindMapHistory': 'History',
  'helpGuide.mindMapHistoryTip': 'Currently, the history feature is not supported for mind maps.',

  // pc 工作动态
  'WorkActivity.whoCanSee': 'Who can see your activity updates?',
  'WorkActivity.whoCanSeeContentP1':
    'Only colleagues from the same company who are involved in file collaboration can see your activity related to that file.',
  'WorkActivity.whoCanSeeContentP2':
    'Activity on private files without added collaborators is visible only to yourself, and your activity in enterprise files cannot be seen by external collaborators.',
  'WorkActivity.updated': 'has updated',
  'WorkActivity.commented': 'has commented',
  'WorkActivity.more': 'and others',
  'WorkActivity.one': '',
  'WorkActivity.file': 'files',
  'WorkActivity.unfoldRest': 'Expand remaining',
  'WorkActivity.piece': '',
  'WorkActivity.edit': 'edit',
  'WorkActivity.updates': 'updates',

  // 效能看板
  'EfficiencyPanel.pageHeader': '{teamName} / Enterprise ID {teamId}',
  'EfficiencyPanel.title': 'Efficiency Panel',
  'EfficiencyPanel.recentDynamic': 'Recent Dynamic',
  'EfficiencyPanel.memberActiveData': 'Member Active Data',
  'EfficiencyPanel.fileActiveData': 'File Active Data',
  'EfficiencyPanel.export': 'Export',
  'EfficiencyPanel.showAllData': 'Show All Data',
  'EfficiencyPanel.leaderboard': 'Leaderboard',
  'EfficiencyPanel.noData': 'No Data',
  'EfficiencyPanel.noSearchResults': 'No Search Results',
  'EfficiencyPanel.nearlyWeekMemberSituation': 'Recent 7-day Enterprise Member Situation',
  'EfficiencyPanel.compareToLastWeek': 'Compare to last week:',
  'EfficiencyPanel.recentSevenDaysCreatedFiles': 'Recent 7-day Created Files',
  'EfficiencyPanel.recentSevenDaysUsage': 'Recent 7-day Usage',
  'EfficiencyPanel.newFile': 'New Files',
  'EfficiencyPanel.collaborativeBehavior': 'Collaborative Behavior',
  'EfficiencyPanel.totalFiles': 'Total Files',
  'EfficiencyPanel.fileClassification': 'File Classification (Unit: pieces)',
  'EfficiencyPanel.ofAll': 'of all',
  'EfficiencyPanel.dailyHotFile': '24h Hot Files',
  'EfficiencyPanel.weeklyHotFile': 'Recent 7-day Hot Files',
  'EfficiencyPanel.latestUpdatesSpace': 'Latest Updated Collaborative Spaces',
  'EfficiencyPanel.fileActiveDataTip':
    'Cumulative data from the creation of the enterprise to date. If the enterprise was created before 2021, the cumulative data from 2021-01-01 to date is provided.',
  'EfficiencyPanel.historicalCreateFileCount': 'Historical Cumulative File Creation Count',
  'EfficiencyPanel.fileUnit': 'files',
  'EfficiencyPanel.historicalCollaborativeBehaviorCount': 'Historical Cumulative Collaborative Behavior Count',
  'EfficiencyPanel.collaborativeBehaviorUnit': 'Collaborative Behavior (Unit: times)',
  'EfficiencyPanel.timesUnit': 'times',
  'EfficiencyPanel.ranking': 'Ranking',
  'EfficiencyPanel.spaceName': 'Space Name',
  'EfficiencyPanel.fileName': 'File Name',
  'EfficiencyPanel.creator': 'Creator',
  'EfficiencyPanel.lastUpdate': 'Last Update',
  'EfficiencyPanel.activatedSeats': 'Activated Seats',
  'EfficiencyPanel.activatedSeatsTip':
    'This week data: Number of seats activated in the last 7 days as of this week; Compared to last week: (this week data - last week data) / last week data * 100%;',
  'EfficiencyPanel.memberActiveDataTip':
    'This week data: Number of active members in the last 7 days; Compared to last week: (this week data - last week data) / last week data * 100%;',
  'EfficiencyPanel.heavyUser': 'Heavy Users',
  'EfficiencyPanel.heavyUserTip':
    'This week data: Number of deeply active members (active for at least 3 days) in the last 7 days; Compared to last week: (this week data - last week data) / last week data * 100%;',
  'EfficiencyPanel.weeklyMostActiveMembers': '7-day Most Active Members',
  'EfficiencyPanel.monthlyMostActiveMembers': 'Monthly Most Active Members',
  'EfficiencyPanel.member': 'Member',
  'EfficiencyPanel.createDocumentCount': 'Created Documents',
  'EfficiencyPanel.addCollaborationCount': 'Added Collaborations',
  'EfficiencyPanel.useAtCount': 'Used @ Count',
  'EfficiencyPanel.publicShareCount': 'Public Shares',
  'EfficiencyPanel.commentCount': 'Comments',
  'EfficiencyPanel.createNewFileNumTip':
    'New file count: Number of files created in the last 7 days, including newly created cloud documents and cloud files',
  'EfficiencyPanel.importedFileNumTip': 'Imported file count: Number of files imported in the last 7 days',
  'EfficiencyPanel.uploadFileNumTip': 'Uploaded cloud file count: Number of files uploaded in the last 7 days',
  'EfficiencyPanel.addCollaborationTimesTip':
    'Add collaboration count: Number of times collaborations were added to team spaces, folders, and files in the last 7 days',
  'EfficiencyPanel.atUserNumTip': 'Use @ count: Number of times @ was used in the last 7 days',
  'EfficiencyPanel.publicShareTip': 'Public share: Number of times public sharing was enabled in the last 7 days',
  'EfficiencyPanel.commentNumTip': 'Comment count: Number of successfully posted comments in the last 7 days',
  'EfficiencyPanel.nearlyWeekTotal': 'Recent 7-day Total',
  'EfficiencyPanel.selectViewDepartmentOrMember': 'Select to view department/member',
  'EfficiencyPanel.recentContact': 'Recent Contact',
  'EfficiencyPanel.department': 'Department',
  'EfficiencyPanel.enterpriseMembers': 'Enterprise Members',
  'EfficiencyPanel.sunday': 'Sun',
  'EfficiencyPanel.monday': 'Mon',
  'EfficiencyPanel.tuesday': 'Tue',
  'EfficiencyPanel.wednesday': 'Wed',
  'EfficiencyPanel.thursday': 'Thu',
  'EfficiencyPanel.friday': 'Fri',
  'EfficiencyPanel.saturday': 'Sat',
  'EfficiencyPanel.lastSunday': 'Last Sun',
  'EfficiencyPanel.lastMonday': 'Last Mon',
  'EfficiencyPanel.lastTuesday': 'Last Tue',
  'EfficiencyPanel.lastWednesday': 'Last Wed',
  'EfficiencyPanel.lastThursday': 'Last Thu',
  'EfficiencyPanel.lastFriday': 'Last Fri',
  'EfficiencyPanel.lastSaturday': 'Last Sat',
  'EfficiencyPanel.yesterday': '1d ago',
  'EfficiencyPanel.dayBeforeYesterday': '2d ago',
  'EfficiencyPanel.weeklyActiveScoreTip':
    'Recent 7-day activity score: File views *1 + File creations *10 + (Add collaborator count + Enable public sharing count + Comment count + Use @ count) *5',
  'EfficiencyPanel.monthlyActiveScoreTip':
    'Recent 30-day activity score: File views *1 + File creations *10 + (Add collaborator count + Enable public sharing count + Comment count + Use @ count) *5',
  'EfficiencyPanel.dailyHotFileTip':
    '24h hot browsed files (including Shimo format files, cloud files, excluding team spaces and folders) TOP 100 (sorted by view count)',
  'EfficiencyPanel.weeklyHotFileTip':
    'Recent 7-day hot browsed files (including Shimo format files, cloud files, excluding team spaces and folders) TOP 100 (sorted by view count)',
  'EfficiencyPanel.latestUpdatesSpaceTip':
    'Collaborative spaces that updated space information in the last 31 days TOP 100 (sorted by update time in descending order, later update time ranks higher)',
  'EfficiencyPanel.timeSavedUnit': 'person/day',
  'EfficiencyPanel.timeSavedTitle': 'Improved enterprise efficiency, total time saved',
  'EfficiencyPanel.autoSaveTitle': 'Reduced enterprise risks, auto-saved',
  'EfficiencyPanel.timeSavedTooltip':
    '(File views *1min + File creations *30mins + Public shares *10mins + Collaborator additions *15mins + Comments *3mins) / (60mins *24h)',
  'EfficiencyPanel.autoSaveTooltip': 'Total auto-save count for this enterprise to date',
  'EfficiencyPanel.proportion': 'Proportion',
  'EfficiencyPanel.documentCreateTip':
    'Documents: Number of documents (including old and new documents) created in the last 7 days',
  'EfficiencyPanel.modocCreateTip': 'Traditional Documents: Number of traditional documents created in the last 7 days',
  'EfficiencyPanel.sheetCreateTip':
    'Sheets: Number of sheets (including old sheets, legacy sheets, professional sheets) created in the last 7 days',
  'EfficiencyPanel.presentationCreateTip':
    'Presentations: Number of presentations (including old presentations, professional presentations) created in the last 7 days',
  'EfficiencyPanel.tableCreateTip': 'Application Tables: Number of application tables created in the last 7 days',
  'EfficiencyPanel.formCreateTip': 'Forms: Number of forms created in the last 7 days',
  'EfficiencyPanel.cloudFileCreateTip': 'Cloud Files: Number of cloud files created in the last 7 days',
  'EfficiencyPanel.otherCreateTip': 'Others: Number of other files (mind maps, whiteboards) created in the last 7 days',

  // Preference
  'Preference.title': 'Preference Settings',
  'Preference.notificationSetting': 'Notification Settings',
  'Preference.operationHabit': 'Operation Habits',
  'Preference.preferenceDescription': 'The system will send emails to {email} to remind you of new messages',
  'Preference.emailNotification': 'Email Notification',
  'Preference.desktopNotification': 'Desktop Notification',
  'Preference.desktopNotificationDesc':
    'The system will pop up bubble reminders for new messages (only valid for Chrome, Firefox, Safari browsers and PC clients)',
  'Preference.habitDefaultOpenFileLocation': 'Where to open files by default?',
  'Preference.habitHowToOpenFile': 'How to open files or folders?',
  'Preference.habitOpenFileDesc':
    'In addition to single click, double clicking the file area can also open files or folders',
  'Preference.habitHomePageLocation': 'Default location when visiting homepage',
  'Preference.habitShowTemplateModal': 'Show template selection window when creating files',
  'Preference.habitShowTemplateModalDesc':
    'When you create a new file, the template selection window will be displayed by default. After closing, blank files will be created directly',
  'Preference.fileLocationNewTab': 'Open in new tab',
  'Preference.fileLocationCurrentTab': 'Open in current tab',
  'Preference.clickFileNameOnly': 'Click file name to open',
  'Preference.clickFileNameOrIcon': 'Click file name or icon to open',
  'Preference.homePageRecent': 'Recent Files',
  'Preference.homePageDesktop': 'My Desktop',
  'Preference.homePageSpace': 'Team Space',
  'Preference.name': 'Name',
  'File.viewType.list': 'List View',
  'File.viewType.grid': 'Grid View',
  'File.viewType.preview': 'Preview View',
  'File.viewType.info': 'File Info',
  'File.action.more': 'More Action',
  'Error.previewError': 'Cannot access this file',
  'Error.previewErrorDes':
    'The reason may be that you do not have access to this file, the file has been deleted, or the file does not exist',
  'Error.folderShortcutError': 'Cannot access this folder',

  // 防打扰
  'DndZone.range': 'Anti-disturbance range',
  'DndZone.disturbableObject': 'Disturbable object',
  'DndZone.effect': 'take effect',
  'DndZone.description':
    'Users or departments can add permissions for collaboration, @Specify the user, and lock cell permissions according to the rules, so as to reduce the number of users and departments in the "anti-disturbance range" to be notified and collaborated.',
  'DndZone.allRoule': 'All rules',
  'DndZone.newRoule': 'New rules have been added',
  'DndZone.noRoule': 'There are no rules at this time,',
  'DndZone.createdRule': 'Click Create',
  'DndZone.firstRoule': 'First rule',
  'DndZone.options': 'Operate',
  'DndZone.edit': 'Edit',
  'DndZone.delete': 'Delete',
  'DndZone.maxRoule': 'Currently, a maximum of {max} rules are supported',

  // 水印-协作者水印设置
  'Watermark.collaborationWatermark': 'Collaboration Watermark Settings',
  'Watermark.enterpriseSetting': 'Enterprise Settings',
  'Watermark.unselectedCustomWatermark': 'Unselected custom watermark will be cleared',
  'Watermark.continueSave': 'Continue Save',
  'Watermark.cancel': 'Cancel',
  'Watermark.saveSuccess': 'Operation successful',
  'Watermark.getInfoFailed': 'Failed to get information, please try again later',
  'Watermark.clearUnselectedContent':
    'There are custom watermarks that have not been selected for use, and they will be automatically cleared when saving. Do you want to continue saving?',
  'Watermark.sheet': 'Sheet',
  'Watermark.doc': 'Doc',
  'Watermark.presentation': 'Presentation',
  'Watermark.form': 'Form',
  'Watermark.table': 'Table',
  'Watermark.cloudFile': 'Cloud File',
  'Watermark.image': 'Image',
  'Watermark.pdf': 'PDF',
  'Watermark.purePdf': 'Pure PDF',
  'Watermark.word': 'Word',
  'Watermark.excel': 'Excel',
  'Watermark.pptx': 'PPTX',
  'Watermark.wps': 'WPS',
  'Watermark.markdown': 'Markdown',
  'Watermark.i18n_shixiaomo': 'Shixiaomo',
  'Watermark.productionDepartment': 'Production Department',
  'Watermark.name': 'Name',
  'Watermark.nickname': 'Nickname',
  'Watermark.id': 'Account ID',
  'Watermark.email': 'Email',
  'Watermark.department': 'Department',
  'Watermark.mobile': 'Mobile',
  'Watermark.time': 'File Open Time',
  'Watermark.hasSameWatermark': 'Same watermark already exists',
  'Watermark.add': 'Add',
  'Watermark.inputTip': 'Input 20 characters or less',
  'Watermark.inputTooLongTip': 'Please enter and press Enter to add',
  'Watermark.addCustomText': 'Add custom text field',
  'Watermark.maxLimit': '10 watermarks selected, reached the limit',
  'Watermark.downloadRangeTipsNoWatermark': 'Downloading as this format file does not support watermarking',
  'Watermark.downloadRangeTip':
    'Enterprise files will be watermarked with collaborator watermarks when downloaded as the selected type (currently supported suite range: documents, tables, presentations)',
  'Watermark.downloadRangeTitle': 'Supported watermarked file download types',
  'Watermark.effectPreview': 'Effect Preview',
  'Watermark.watermarkTimeSetting': 'Watermark Time Setting',
  'Watermark.watermarkFieldsListTitle': 'Watermark Fields List',
  'Watermark.watermarkFieldsListCount': 'Max 10, Selected ',
  'Watermark.one': ' ',
  'Watermark.currentSettingNotChanged': 'Current setting not changed',
  'Watermark.saveSetting': 'Save Setting',
  'Watermark.watermarkSpreadRangeTip':
    'Enterprise members will see watermarks with their account information when accessing enterprise files in the login state. Enable watermarks to reduce the risk of content leakage and hold responsible for information leaks. Currently, watermarks are not displayed for personal users and users who access public links without logging in.',
  'Watermark.watermarkSpreadRangeTitle': 'Set watermark display range',
  'Watermark.timeZone': 'Time Zone',
  'Watermark.timeFormat': 'Time Format',

  // 离职交接
  'hanover.cannotHandoverSelf': 'Cannot hand over your own files',
  'hanover.contactAdmin': 'Please contact the administrator to rearrange the handover',
  'hanover.invitationExpired': 'Handover invitation has expired',
  'hanover.noPermission': 'You do not have permission to view this page',
  'hanover.sameOrgRequired': 'Only users from the same organization can participate in file handover',
  'hanover.pleaseLoginWeb': 'Please log in and access this link via the web',
  'hanover.loading': 'Loading',
  'hanover.manageHandover': 'Manage {type}',
  'hanover.handoverOut': 'Resignation Handover',
  'hanover.handoverFile': 'File Handover',
  'hanover.batchHandovering': 'Bulk file handover in progress, please wait',
  'hanover.logout': 'Logout',
  'hanover.selectedFilesCount': 'Selected handover files: {count}',
  'hanover.selectHandoverFile': 'Select handover files',
  'hanover.confirmEnd': 'Confirm end',
  'hanover.confirmEndTitle': 'Confirm to end this handover',
  'hanover.endHandoverConfirm':
    'After ending this handover, no one will be able to access the current handover link. Are you sure you want to end it?',
  'hanover.restartHandoverTip':
    'You can initiate the handover process for {transfereeName} again. The file list for the new handover may not include files marked as "Handed Over" in this process.',
  'hanover.pending': 'Not handed over',
  'hanover.deleted': 'Deleted',
  'hanover.completed': 'Handed over',
  'hanover.waiting': 'Pending handover',
  'hanover.handoverStatus': 'Handover status: ',
  'hanover.handoverPerson': 'Handover person: ',
  'hanover.handoverCompleted': 'Handover completed',
  'hanover.handoverInProgress': 'Handover in progress',
  'hanover.allFiles': 'All files',
  'hanover.allHandoverFiles': 'All handover files',
  'hanover.completedFiles': 'Handed over files',
  'hanover.waitingFiles': 'Pending handover files',
  'hanover.unhandoverFiles': 'Unhanded files',
  'hanover.batchTakeover': 'Batch acquire management rights',
  'hanover.folderHandoverTip':
    'The current directory only supports handing over all sub-files. To hand over the entire folder, go to the parent directory to obtain folder management rights.',
  'hanover.selectAll': 'Select all',
  'hanover.noData': 'No data',
  'hanover.transfereeFiles': "Current transferee's files",
  'hanover.allHandovered': 'All handed over',
  'hanover.initiateHandoverTips':
    'You are initiating the handover process for {name}. Please send the following content to the transferee.',
  'hanover.operationSuccess': 'Operation successful',
  'hanover.accountDisabled': 'Your account has been disabled by the administrator. Please contact the administrator.',
  'hanover.sameCompanyRequired': 'Must be an account from the same company',
  'hanover.fileAlreadyHandovered': 'This file has already been handed over',
  'hanover.fileName': 'File name',
  'hanover.fileNameMayChanged': 'After handover, the file name may have been modified',
  'hanover.selectedCount': 'Selected {count} files',
  'hanover.action': 'Action',
  'hanover.takeoverSuccess': 'Successfully acquired management rights',
  'hanover.takeover': 'Acquire management rights',
  'hanover.currentHandoverFiles': 'Files in current handover',
  'hanover.currentHandoverTips': 'Files that have been handed over by members in this process',
  'hanover.waitingHandoverTips': 'No files have been handed over by members in this process',
  'hanover.notInHandoverFiles': 'Files not in current handover',
  'hanover.unhandoverFilesTips': 'Files of the current user not included in the handover process',
  'hanover.addedFilesCount': 'Files added in this batch ({count})',
  'hanover.clear': 'Clear',
  'hanover.noFilesAdded': 'No files added yet',
  'hanover.selectFilesTip': 'You can check and add files on the left',
  'hanover.handoverLink':
    'You are the transferee for {handoverName}. Please access this link via the web within 7 days to complete the file handover:\n{handoverUrl} \n  \nIf the link has expired but the handover is not completed, please contact the administrator to reinitiate the process.',
  'hanover.manageHandoverTitle': 'Manage file handover',
  'hanover.personHandoverTitle': "{name}'s file handover",
  'hanover.linkExpiryTime': 'Link expiry time: {time}',
  'hanover.copyLink': 'Copy handover link',
  'hanover.endHandover': 'End this handover',
  'hanover.fileListTips':
    "The file list displays all files, folders, and team spaces involved in the current transferee's handover. Click the online file title to preview the file summary.",
  'hanover.confirmInfo':
    'Please confirm the following information. After confirmation, send the handover link to the transferee.',
  'hanover.historyFilesTitle': 'Historical handover files',
  'hanover.historyFilesTips':
    'The file list displays files that have been successfully handed over in historical handover processes.',
  'hanover.previewEmpty': 'Empty',
  'hanover.previewLoadFail': 'Failed to load',
  'hanover.previewLoading': 'File preview loading',
  'hanover.filePreview': 'File preview',
  'hanover.handovered': 'A handover process for this member has been initiated. Please refresh the page to view.',
  'hanover.previousStep': 'Previous step',
  'hanover.nextStep': 'Next step',
  'hanover.selectFiles': 'Please select files to hand over',
  'hanover.fileSelect': 'Handover file selection',
  'hanover.cancel': 'Cancel',
  'hanover.confirmHandover': 'Confirm to initiate handover process',
  'hanover.handovering': 'Initiating handover',
  'hanover.copy': 'Copy text',
  'hanover.close': 'Close',
  'hanover.currentHandoverPerson': 'Current handover person',
  'hanover.transfereePerson': 'Transferee',
  'hanover.fileNameChanged':
    'After initiating the handover process, if the file name is modified, the updated name will not be synchronized in the list.',
  'hanover.ok': 'Got it',
  'hanover.confirm': 'Confirm',
  'hanover.fail': 'Handover failed',
  'hanover.failTips': 'The handover link has expired. Please contact the administrator to rearrange the handover.',
  'hanover.viewHistoricalHandoverFiles': 'View historical handover files',
  'hanover.initiateHandover': 'Initiate {type}',

  // 容量管理
  'CapacityManage.capacityManagement': 'Capacity Management',
  'CapacityManage.search': 'Search',
  'CapacityManage.allSpace': 'All team spaces',
  'CapacityManage.selectAll': 'Select all',
  'CapacityManage.goBack': 'Go back to the first level',
  'CapacityManage.noData': 'No data',
  'CapacityManage.batchModify': 'Batch modify',
  'CapacityManage.previous': 'Previous',
  'CapacityManage.next': 'Next',
  'CapacityManage.creator': 'Creator',
  'CapacityManage.enterpriseRemainingCapacity': 'Enterprise remaining capacity',
  'CapacityManage.enterpriseTotalCapacity': 'Enterprise total capacity',
  'CapacityManage.totalCapacity': 'Total capacity',
  'CapacityManage.setCapacitySize': 'Set capacity size',
  'CapacityManage.specifyCapacitySize': 'Specify capacity size',
  'CapacityManage.specifySize': 'Specify size',
  'CapacityManage.noLimit': 'No limit',
  'CapacityManage.hasNoLimit': 'Unlimited',
  'CapacityManage.supportOverAllocation':
    'Support over allocation, that is, the total size of the quota is not limited by the total capacity of the enterprise',
  'CapacityManage.saveModify': 'Save modification',
  'CapacityManage.usedCapacity': 'Used capacity',
  'CapacityManage.capacityOverview': 'Capacity overview',
  'CapacityManage.sourceDetails': 'Source details',
  'CapacityManage.usageDetails': 'Usage details',
  'CapacityManage.usageOverview': 'Usage overview',
  'CapacityManage.enterpriseMembersCapacityMaximum': 'Enterprise Member Capacity Limit',
  'CapacityManage.memberDesktopCapacityLimit': 'Member desktop capacity limit',
  'CapacityManage.teamSpaceCapacityLimit': 'Team space capacity limit',
  'CapacityManage.saveSuccess': 'Save success',
  'CapacityManage.saveFailed': 'Save failed',
  'CapacityManage.member': 'Member',
  'CapacityManage.email': 'Email',
  'CapacityManage.distributionMode': 'Distribution mode',
  'CapacityManage.allocatedCapacity': 'Allocated capacity',
  'CapacityManage.myDesktopCapacity': 'My desktop capacity',
  'CapacityManage.personalTrashCapacity': 'Personal trash capacity',
  'CapacityManage.teamSpaceCapacity': 'Team space capacity',
  'CapacityManage.teamSpaceCapacityTip':
    'The total capacity occupied by the files created and uploaded by this member in the team space',
  'CapacityManage.modify': 'Modify',
  'CapacityManage.viewDetails': 'View details',
  'CapacityManage.operation': 'Operation',
  'CapacityManage.customEnterpriseMemberCapacityLimit': 'Custom enterprise member capacity limit',
  'CapacityManage.customMemberDesktopCapacityLimit': 'Custom member desktop capacity limit',
  'CapacityManage.searchEnterpriseMember': 'Search enterprise members by name / email',
  'CapacityManage.selectEnterpriseMember': 'Select enterprise member',
  'CapacityManage.pleaseSearchOrSelectQuotaObject': 'Please search or select quota object:',
  'CapacityManage.thisTimeAddedMembers': 'This time added members',
  'CapacityManage.clear': 'Clear',
  'CapacityManage.noMembersAdded': 'No members added yet',
  'CapacityManage.canSearchOrExpandAndAdd': 'You can search or expand on the left to add',
  'CapacityManage.selectedEnterpriseMember': 'Selected enterprise member:',
  'CapacityManage.people': 'people',
  'CapacityManage.operationSuccessful': 'Operation successful',
  'CapacityManage.failedToSetCustomCapacity': 'Failed to set custom capacity',
  'CapacityManage.currentMemberCapacityUsageCalculationComplete':
    'The current member capacity usage calculation is complete, please refresh the page, and the subsequent usage capacity will be automatically updated',
  'CapacityManage.memberCapacityUsageCalculationFailed':
    'Member capacity usage calculation failed, please try again later',
  'CapacityManage.startCalculating': 'Start calculating',
  'CapacityManage.calculateMemberCapacityUsage': 'Start calculating member capacity usage',
  'CapacityManage.calculateMemberCapacityUsageTip1':
    'The calculation task is running in the background and will not affect other operations.',
  'CapacityManage.calculateMemberCapacityUsageTip2':
    'After the calculation is completed, the result will be automatically updated to the list, and the data will be kept synchronized afterwards.',
  'CapacityManage.calculateMemberCapacityUsageTip3':
    'If there are many enterprise members, the calculation may take several minutes to several hours, please check the results later.',
  'CapacityManage.memberUsageDetailsCalculationInProgress': 'Member usage details calculation in progress',
  'CapacityManage.clickToTriggerCapacityCalculationTask':
    'Click this button to trigger the capacity calculation task, and get the capacity usage of enterprise members in the desktop, personal trash, and team space.',
  'CapacityManage.calculateMemberUsageDetails': 'Calculate member usage details',
  'CapacityManage.customTeamSpaceCapacityLimit': 'Custom team space capacity limit',
  'CapacityManage.searchTeamSpace': 'Search team space by name',
  'CapacityManage.name': 'Name',
  'CapacityManage.creatorEmail': 'Creator email',
  'CapacityManage.selectedTeamSpace': 'Selected team space',
  'CapacityManage.noTeamSpace': 'No team space added yet',
  'CapacityManage.searchOrSelectQuotaObject': 'Please search or select quota object:',
  'CapacityManage.goToSetting': 'Go to setting',
  'CapacityManage.enterpriseMemberCount': 'Enterprise member count',
  'CapacityManage.capacityRatio': 'Capacity ratio',
  'CapacityManage.customCapacityLimit': 'Custom capacity limit',
  'CapacityManage.teamSpaceCount': 'Team space count',
  'CapacityManage.isEnterpriseRecycleBinAutoCleanEnabled': 'Is enterprise recycle bin auto clean enabled',
  'CapacityManage.enterpriseTrashFileCount': 'Enterprise trash file count',
  'CapacityManage.noUsageRights': 'No usage rights',
  'CapacityManage.customCapacity': 'Custom capacity',
  'CapacityManage.defaultCapacity': 'Default capacity',
  'CapacityManage.resetDefaultCapacity': 'Reset default capacity',
  'CapacityManage.disable': 'Disable',
  'CapacityManage.defaultEnterpriseMemberCapacityLimit': 'Default enterprise member capacity limit',
  'CapacityManage.defaultMemberDesktopCapacityLimit': 'Default member desktop capacity limit',
  'CapacityManage.defaultTeamSpaceCapacityLimit': 'Default team space capacity limit',
  'CapacityManage.defaultEnterpriseMemberCapacityLimitTip':
    'The total capacity occupied by the files created and uploaded by this member in the desktop, personal trash, and team space',
  'CapacityManage.defaultMemberDesktopCapacityLimitTip':
    'The total capacity occupied by the files created and uploaded by this member in the desktop, personal trash, and team space',
  'CapacityManage.setDefaultEnterpriseMemberCapacityLimit': 'Set default enterprise member capacity limit',
  'CapacityManage.setDefaultMemberDesktopCapacityLimit': 'Set default member desktop capacity limit',
  'CapacityManage.setDefaultTeamSpaceCapacityLimit': 'Set default team space capacity limit',
  'CapacityManage.defaultEnterpriseRecycleBinCapacityLimitTip':
    'The total capacity occupied by the files deleted by this member in the enterprise recycle bin',
  'CapacityManage.defaultTeamSpaceCapacityLimitTip':
    'The total capacity occupied by the files created and uploaded by this member in the team space',
  'CapacityManage.memberDesktopUsage': 'Member desktop usage',
  'CapacityManage.teamSpaceUsage': 'Team space usage',
  'CapacityManage.enterpriseRecycleBinUsage': 'Enterprise recycle bin usage',
  'CapacityManage.memberDesktopUsageTitle': 'Member desktop usage',
  'CapacityManage.teamSpaceUsageTitle': 'Team space usage',
  'CapacityManage.enterpriseRecycleBinUsageTitle': 'Enterprise recycle bin usage',
  'CapacityManage.remainingCapacity': 'Remaining capacity',
  'CapacityManage.usage': 'Usage',
  'CapacityManage.enabled': 'Enabled',
  'CapacityManage.disabled': 'Disabled',
  'CapacityManage.enable': 'Enable',
  'CapacityManage.piece': '',
  //角色管理
  'Role.roleManagement': 'Role Management',
  'Role.manageRole': 'Manage role permissions or role members, create custom roles, and transfer to the enterprise',
  'Role.efficiency': 'Efficiency Dashboard',
  'Role.performance':
    'View the latest updates, historical accumulation, and ranking data on the performance dashboard page.',
  'Role.payment': 'Payment and Orders',
  'Role.enterprisePaymentDesc': 'Renew enterprise subscription, purchase additional seats, view order history.',
  'Role.viewContact': 'View Contact List',
  'Role.viewContactDesc': 'View the enterprise contact list.',
  'Role.inviteNewMember': 'Invite New Members',
  'Role.inviteNewMemberDesc': 'Invite new members to join the enterprise.',
  'Role.manageContact': 'Manage Contact List',
  'Role.manageContactDesc':
    'Manage enterprise organizational structure, adjust member departments, disable members, etc.',
  'Role.operationLog': 'Operation Log',
  'Role.operationLogDesc': 'View operation logs, filter and export logs.',
  'Role.managePublicShareLinks': 'Manage Public Sharing Links',
  'Role.managePublicShareLinksDesc':
    'View all currently shared public links for the enterprise and individually or in bulk modify sharing status.',
  'Role.allowMembersExit': 'Allow Enterprise Members to Exit Voluntarily',
  'Role.allowMembersExitDesc': 'Set whether enterprise members are allowed to exit voluntarily.',
  'Role.fileSecurityAlert': 'File Security Alerts',
  'Role.fileSecurityAlertDesc':
    'Set up alert switches and trigger thresholds when enterprise members perform file operations.',
  'Role.collaboratorWatermark': 'Display Watermarks for Collaborators on Enterprise Files',
  'Role.collaboratorWatermarkDesc': 'Set which office suites display watermarks and their content.',
  'Role.fileOperationControl': 'Enterprise-wide File Operation Controls',
  'Role.fileOperationControlDesc':
    'Implement enterprise-wide permission controls over operations such as collaborator management, download, print, create copies, etc.',
  'Role.enterpriseRecycleBin': 'Enterprise Recycle Bin',
  'Role.enterpriseRecycleBinDesc':
    'Find files deleted by all enterprise members due to recycle bin clearance, and restore or permanently delete them.',
  'Role.createTeamSpace': 'Create Team Space',
  'Role.createTeamSpaceDesc':
    'Restrict permissions for creating spaces to standardize directory management within the enterprise.',
  'Role.capacityManagement': 'Capacity Management',
  'Role.capacityManagementDesc':
    'View storage capacity usage of all enterprise members and allocate capacity precisely.',
  'Role.roleManagementDesc':
    'Create a new role, edit a role, the user bound to the new role, and the administrator bound to the edit role.',
  'Role.contactManagement': 'Contact Management',
  'Role.existSameNameRole': 'A role with the same name already exists',
  'Role.createRoleFailed': 'Failed to Create Role',
  'Role.modifyPermission': 'Modify Permissions',
  'Role.newRole': 'New Role',
  'Role.getRolePermissionFailed': 'Failed to Retrieve Role Permissions',
  'Role.viewPermission': 'View Permissions',
  'Role.deleteRole': 'Delete Role',
  'Role.cannotDeleteRole': 'Cannot Delete Role',
  'Role.pleaseRemoveAllMembersBeforeDeletingRole': 'Please remove all members bound to this role before deleting it.',
  'Role.confirm': 'Confirm',
  'Role.deleteRoleConfirm': 'Confirm Deletion of Role?',
  'Role.deleteRoleTip': 'After deletion, the permissions configured for this role will also be cleared.',
  'Role.deleteConfirm': 'Confirm Deletion',
  'Role.cancel': 'Cancel',
  'Role.deleteSuccess': 'Successfully Deleted',
  'Role.deleteError': 'Deletion Failed',
  'Role.removeMember': 'Remove Member',
  'Role.removeMemberConfirm': 'Confirm Removal of Member?',
  'Role.removeMemberTip': "After removal, the member's permissions associated with this role will also be cleared.",
  'Role.removeMemberConfirmTitle': 'Confirm Removal',
  'Role.removeMemberSuccess': 'Successfully Removed',
  'Role.modifyPermissionTitle': 'Upgrade to Enterprise Premium to Modify Permissions',
  'Role.bindUser': 'Bind User',
  'Role.bindUserTitle': 'Upgrade to Enterprise Edition to Bind Users',
  'Role.batchDelete': 'Batch Delete',
  'Role.removeMemberRoleCountnt': 'Selected {length} members. Confirm batch removal?',
  'Role.removeMemberRoleTip': "After removal, the member's permissions associated with this role will also be cleared.",
  'Role.removeMemberRoleSuccess': 'Successfully Removed',
  'Role.roleList': 'Role List',
  'Role.upgradeRole': 'Upgrade to Enterprise Premium to Create Roles',
  'Role.permissionMo': '{name} Permissions',
  'Role.selectApp': 'Please Search or Select Application Object',
  'Role.search': 'Search',
  'Role.selectedMember': 'Selected Members',
  'Role.clear': 'Clear',
  'Role.transferEnterprise': 'Transfer Enterprise Ownership',
  'Role.removeMemberRole': 'Remove',
  'Role.transferEnterpriseTips':
    'Confirm transfer of enterprise? After transfer, the user will be downgraded to a regular enterprise member.',
  'Role.transferEnterpriseConfirm': 'Confirm Transfer',
  'Role.maxTransferNumber': 'Maximum of {max} members can be transferred',
  'Role.selectMember': 'Please Search or Select Member',
  'Role.addedMember': 'Members to Add This Time',
  'Role.defaultRole': 'Default Role Cannot Be Modified',
  'Role.listDefaultRoleTips': 'This list will not be displayed',
  'Role.listNoUserTips': 'No users assigned to this role',
  'Role.roleName': 'Role Name',
  'Role.rolePermission': 'Role Permissions',
  'Role.noPermission': 'Currently, no permissions have been assigned to this role.',
  'Role.upgradeUse': 'Upgrade to Use',
  'Role.enterpriseCreator': 'Enterprise Creator',
  'Role.enterpriseAdmin': 'Enterprise Administrator',
  'Role.enterpriseMember': 'Regular Enterprise Member',
  'Role.totalPeople': 'Total number of people',

  //组织架构查看权限
  'Organization.organizationPermission': 'Organizational structure authority',
  'Organization.viewDetails': 'For more details',
  'Organization.operationSuccess': 'Operation Successful',
  'Organization.operationFailed': 'Operation Failed',
  'Organization.noPermission': 'No Permission',
  'Organization.ruleNotExist': 'Rule does not exist, please refresh and try again',
  'Organization.deleteFailed': 'Deletion Failed',
  'Organization.deleteSuccess': 'Successfully Deleted',
  'Organization.edit': 'Edit',
  'Organization.delete': 'Delete',
  'Organization.visible': 'Visible',
  'Organization.invisible': 'Invisible',
  'Organization.etcOwners': 'and {count} more objects',
  'Organization.etcTargets': 'and {count} more scopes',
  'Organization.applicableObject': 'Applicable Object',
  'Organization.rule': 'Rule',
  'Organization.scope': 'Scope',
  'Organization.operation': 'Operation',
  'Organization.effective': 'Effective',
  'Organization.searchOrSelectScope': 'Please search or select a scope',
  'Organization.searchOrSelectObject': 'Please search or select an applicable object',
  'Organization.search': 'Search',
  'Organization.selected': 'Selected Departments/Members',
  'Organization.clear': 'Clear',
  'Organization.operationFailedRety': 'Operation failed, please try again',
  'Organization.ruleLimit': 'Current supplementary rule limit reached, cannot add more',
  'Organization.apply': 'Apply',
  'Organization.next': 'Next',
  'Organization.range': 'Range',
  'Organization.applyObject': 'Applicable Object',
  'Organization.maxSelect': 'Maximum of ${count} items can be selected',
  'Organization.previous': 'Previous',
  'Organization.ruleSetting': 'Supplementary Rule Settings',
  'Organization.setRule': 'Set Rule',
  'Organization.setRange': 'Set Scope',
  'Organization.selectApplyObject': 'Select Applicable Object',
  'Organization.noRule': 'No supplementary rules available',
  'Organization.supplementRule': 'Supplementary Rule',
  'Organization.supplementRuleTip':
    'Set user or department visibility for organizational structure based on rules (supplementary rules take precedence over basic rules)',
  'Organization.addSupplementRule': 'Add New Supplementary Rule',
  'Organization.addSupplementRuleTip':
    'If a member is not visible, they also cannot be found via search (including collaboration addition, @mention, cell locking permissions, etc.)',
  'Organization.cancel': 'Cancel',
  'Organization.confirmDelete': 'Confirm Deletion',
  'Organization.confirmDeleteRen': 'Confirm Delete',
  'Organization.confirmDeleteRule': 'Are you sure you want to delete this rule?',
  'Organization.enterpriseSetting': 'Enterprise Settings',
  'Organization.securitySetting': 'Security Settings',
  'Organization.basicRule': 'Basic Rule',
  'Organization.basicRuleTip': 'When no supplementary rules are set, determine visibility based on the basic rule',
  'Organization.basicRuleTip2': 'Also visible: sub-departments and their members',
  'Organization.showDepartmentMember': 'Show Department Member Count',
  'Organization.backToFirstLevel': 'Return to Top Level',
  'Organization.noMember': 'No members added yet',
  'Organization.searchOrExpand': 'You can search or expand from the left, check to add',
  'Organization.noData': 'No Data Available',
  'Organization.allMemberVisibleAllDepartment': 'All members can see all departments',
  'Organization.allMemberVisibleThisDepartment': 'All members can see everyone in this department',
  'Organization.allMemberInvisible': 'All members cannot see anyone',
  'Organization.transferEnterprise': 'Transfer Enterprise',
  'Organization.getDataFailedPleaseRetry': 'Failed to fetch data, please retry',
  // 翻译

  'fileAlert.title': 'File Security Alert',
  'fileAlert.desc1':
    'When you set file-related permissions, you can use this alert switch to set the frequency of operations on files by personnel.',
  'fileAlert.desc2':
    'For example: If the number of files exported by a single person exceeds the threshold, an email and notification will be sent. Enabling the switch can better prevent enterprise file leakage.',
  'fileAlert.save': 'Save Settings',
  'fileAlert.notChanged': 'No changes made to current settings',
  'fileAlert.adminAndCreator': 'Enterprise Creator and Admin',
  'fileAlert.creator': 'Enterprise Creator',
  'fileAlert.admin': 'Admin',
  'fileAlert.notifyLabel': 'Notifications will be sent by email to',
  'fileAlert.success': 'Operation successful',
  'fileAlert.limitError': 'Please enter a number between 1 and 99',
  'fileAlert.breadcrumb': 'Enterprise Settings',
};

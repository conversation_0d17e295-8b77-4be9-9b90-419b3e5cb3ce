import { Modal, Skeleton } from 'antd';
import { useMemo } from 'react';

import { ReactComponent as ArrowLeft } from '@/assets/images/svg/arrow-left.svg';

import { CloseModalIcon } from './components/close-modal-icon';
import { Footer } from './components/main-footer';
import { MainView } from './components/main-view';
import { UserAdd } from './components/user-add';
import { UserList } from './components/user-list';
import { useFilePermission, ViewType } from './hooks/use-file-permission';
import styles from './index.less';

function ModalTitle({ title, goBack, showGoBack }: { title: string; showGoBack: boolean; goBack: () => void }) {
  return (
    <div className={styles.title} onClick={showGoBack ? goBack : undefined}>
      {showGoBack && <ArrowLeft height={22} width={22} />}
      <span className={styles.titleText}>{title}</span>
    </div>
  );
}

interface ViewRendererProps {
  currentView: ViewType;
  goNext: ReturnType<typeof useFilePermission>['goNext'];
  tabList: ReturnType<typeof useFilePermission>['tabList'];
  updateData: ReturnType<typeof useFilePermission>['updateData'];
  transformCollapseList: ReturnType<typeof useFilePermission>['transformCollapseList'];
  currentUsers: ReturnType<typeof useFilePermission>['currentUsers'];
  currentPermissionId: ReturnType<typeof useFilePermission>['currentPermissionId'];
}

function ViewRenderer({
  currentView,
  goNext,
  tabList,
  updateData,
  transformCollapseList,
  currentUsers,
  currentPermissionId,
}: ViewRendererProps) {
  const viewComponents = useMemo(
    () => ({
      [ViewType.MAIN]: (
        <MainView
          goNext={goNext}
          tabList={tabList}
          transformCollapseList={transformCollapseList}
          updateData={updateData}
        />
      ),
      [ViewType.USER_LIST]: (
        <UserList
          currentPermissionId={currentPermissionId}
          goNext={goNext}
          list={currentUsers}
          updateData={updateData}
        />
      ),
      [ViewType.USER_ADD]: (
        <UserAdd currentPermissionId={currentPermissionId} currentUsers={currentUsers} updateData={updateData} />
      ),
    }),
    [goNext, tabList, transformCollapseList, updateData, currentUsers, currentPermissionId],
  );

  return viewComponents[currentView];
}

export function FilePermissionModal({ onClose }: { onClose: () => void }) {
  const {
    title,
    currentView,
    goBack,
    showGoBack,
    goNext,
    tabList,
    updateData,
    transformCollapseList,
    currentUsers,
    isLoading,
    changed,
    onSave,
    currentPermissionId,
  } = useFilePermission();

  return (
    <Modal
      open
      className={styles.filePermissionModal}
      closeIcon={<CloseModalIcon changed={changed} onClose={onClose} />}
      footer={null}
      keyboard={false}
      maskClosable={false}
      title={<ModalTitle goBack={goBack} showGoBack={showGoBack} title={title} />}
      width={556}
    >
      {isLoading ? (
        <Skeleton />
      ) : (
        <>
          <ViewRenderer
            currentPermissionId={currentPermissionId}
            currentUsers={currentUsers}
            currentView={currentView}
            goNext={goNext}
            tabList={tabList}
            transformCollapseList={transformCollapseList}
            updateData={updateData}
          />
          {!showGoBack && <Footer changed={changed} showCheckbox={false} onClose={onClose} onSave={onSave} />}
        </>
      )}
    </Modal>
  );
}

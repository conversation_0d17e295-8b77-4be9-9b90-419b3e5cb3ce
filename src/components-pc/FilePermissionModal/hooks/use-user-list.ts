import type { CheckboxProps } from 'antd';
import { useMemo } from 'react';

import type { useFilePermission } from './use-file-permission';

export interface UserListProps {
  goNext: () => void;
  list: ReturnType<typeof useFilePermission>['currentUsers'];
  updateData?: ReturnType<typeof useFilePermission>['updateData'];
  currentPermissionId: ReturnType<typeof useFilePermission>['currentPermissionId'];
}

export function useUserList({ goNext, list, updateData, currentPermissionId }: UserListProps) {
  const titleText = '已添加的指定成员';
  const addText = '添加更多成员';
  const toolTips = '包含继承自企业设置的成员，以及单独为此空间添加的成员';
  const checkText = '全选';

  const checkedList = useMemo(
    () => list.filter((item) => item.checked).map((user) => user.targetId.toString()),
    [list],
  );
  // 将成员列表转换为checkbox选项格式
  const checkboxOptions = list.map((user) => ({
    label: user.name,
    value: user.targetId.toString(),
  }));

  const checkAll = checkedList.length === list.length;
  const indeterminate = checkedList.length > 0 && checkedList.length < list.length;

  const onChange = (checked: boolean, targetId: number) => {
    if (updateData && currentPermissionId) {
      updateData(currentPermissionId, (prev) => {
        return {
          user: prev.user?.map((user) => (user.targetId === targetId ? { ...user, checked } : user)),
        };
      });
    }
  };

  const onCheckAllChange: CheckboxProps['onChange'] = (e) => {
    const checked = e.target.checked;
    if (updateData && currentPermissionId) {
      updateData(currentPermissionId, () => ({
        user: list.map((user) => ({ ...user, checked })),
      }));
    }
  };

  const onClickNext = () => {
    goNext();
  };

  return {
    titleText,
    addText,
    toolTips,
    checkText,
    checkboxOptions,
    checkAll,
    indeterminate,
    onChange,
    onCheckAllChange,
    onClickNext,
    checkedList,
    list,
  };
}

import debounce from 'lodash/debounce';
import { useCallback, useMemo, useRef, useState } from 'react';

import { type CrumbsType, type OrgItemType, type OrgSelectAll, OrgType } from '@/components/OrgSelectCard/type';
import { DEBOUNCE_INPUT_WAIT, DEFAULT_PAGE_CURRENT, DEFAULT_PAGE_SIZE, ROOT_DEPARTMENT, ZERO } from '@/configs/configs';
import type { OrgSelectType } from '@/hooks/useOrgSelect';
import { useOrgSelectSearch } from '@/hooks/useOrgSelectSearch';
import type { User } from '@/service/files-team-settings.type';
import { getOrgTree, getUserList } from '@/service/userOrg/service';
import { convertCrumbs, convertOrgList } from '@/utils/orgSelect';

interface OrgSelectParams {
  pageSize?: number;
  checkDepartment?: boolean; // 是否可以选择企业，默认为 true
  searchUserConfig?: {
    includeDisabledMember?: boolean;
    includeRecentContact?: boolean;
    includeTeamMember?: boolean;
  };
  ignorePending?: boolean; // 忽略「邀请中」的用户
  admin?: boolean; // 是否拥有管理权限（无视组织架构可见性的过滤），默认为 false
  enableSelectAllEnterprise?: boolean;
  onUpdate: (users: User[]) => void;
}

const transformSelectedList = (list: OrgItemType[]) => {
  return list.map((item) => ({
    targetId: item.id,
    name: item.title,
    avatar: item.avatar || '',
    email: item.email || '',
    allow: true,
    inherited: true,
  }));
};

export function useUserSelect({
  pageSize = DEFAULT_PAGE_SIZE,
  checkDepartment = true,
  searchUserConfig,
  ignorePending,
  admin = false,
  onUpdate,
}: OrgSelectParams): OrgSelectType {
  const [loading, setLoading] = useState(false);
  const [orgList, setOrgList] = useState<OrgItemType[]>([]);
  const [selectedList, setSelectedList] = useState<OrgItemType[]>([]);
  const [currentCrumb, setCurrentCrumb] = useState(ROOT_DEPARTMENT);
  const [crumbs, setCrumbs] = useState<CrumbsType[]>();
  const pageRef = useRef(DEFAULT_PAGE_CURRENT);
  const isEndRef = useRef(false);
  const isPendingRef = useRef(false);
  const search = useOrgSelectSearch({
    selectedList,
    checkDepartment,
    searchUserConfig,
    admin,
  });

  // 处理用户选择变更
  const handleUserSelectionChange = useCallback(
    (selectedUsers: User[]) => {
      onUpdate(selectedUsers);
    },
    [onUpdate],
  );

  const getOrgList = useCallback(
    (params: { page: number; departmentId: number; init?: OrgItemType[]; ignorePending?: boolean }) => {
      if (isPendingRef.current) {
        return;
      }
      isPendingRef.current = true;
      getOrgTree({ perPage: pageSize, ...params }, admin)
        .then((res) => {
          if (res !== null) {
            const { tree } = res;
            setCurrentCrumb(tree.id);
            setCrumbs((list) =>
              convertCrumbs({
                list: list ?? [],
                crumb: { id: tree.id, title: tree.title },
              }),
            );
            setOrgList((list) =>
              pageRef.current === params.page
                ? list.concat(
                    convertOrgList({
                      list: tree?.children,
                      checkDepartment,
                    }),
                  )
                : list,
            );
            pageRef.current = DEFAULT_PAGE_CURRENT;
            isEndRef.current = false;
          } else {
            setCrumbs(undefined);
            setOrgList([]);
          }
        })
        .finally(() => {
          isPendingRef.current = false;
          setLoading(false);
        });
    },
    [pageSize, checkDepartment, admin],
  );

  const addSelected = useCallback(
    (addList: OrgItemType[], isInit: boolean = false) => {
      const updateSelectedHandler = (arr: OrgItemType[], cur: OrgItemType) => {
        if (!arr.find((item) => item.id === cur.id)) {
          arr.push(cur);
        }
        return arr;
      };
      const newList = [...selectedList, ...addList].reduce(updateSelectedHandler, [] as OrgItemType[]);
      setSelectedList(newList);
      if (!isInit) {
        handleUserSelectionChange(transformSelectedList(newList));
      }
    },
    [handleUserSelectionChange, selectedList],
  );

  const clearAllSelected = useCallback(() => {
    setSelectedList([]);
    search.clearAllSelected();
    handleUserSelectionChange([]);
  }, [handleUserSelectionChange, search]);

  const cancelSelected = useCallback(
    (id: number) => {
      const newList = selectedList.filter((item) => item.id !== id);
      handleUserSelectionChange(transformSelectedList(newList));
      setSelectedList(newList);
    },
    [handleUserSelectionChange, selectedList],
  );

  const onPressDepartment = useCallback(
    (id: number) => {
      setLoading(true);
      setOrgList([]);
      getOrgList({
        page: DEFAULT_PAGE_CURRENT,
        departmentId: id,
        ignorePending: ignorePending,
      });
    },
    [getOrgList, ignorePending],
  );

  const onCheckHandler = useCallback(
    (org: OrgItemType) => {
      if (!org.checked) {
        const newList = selectedList.concat({ ...org, checked: true });
        handleUserSelectionChange(transformSelectedList(newList));
        setSelectedList(newList);
        return;
      }
      cancelSelected(org.id);
    },
    [cancelSelected, handleUserSelectionChange, selectedList],
  );

  const clickCrumbsHandler = useCallback(
    (id: number) => {
      setLoading(true);
      setOrgList([]);
      getOrgList({
        page: DEFAULT_PAGE_CURRENT,
        departmentId: id,
        ignorePending,
      });
    },
    [getOrgList, ignorePending],
  );

  const orgSelectAll = useMemo((): OrgSelectAll => {
    const filterList = orgList.filter((item) => item.checkable);
    const isSelected = (item: OrgItemType) => selectedList.some((selected) => selected.id === item.id);
    const checked = filterList.length > ZERO && filterList.every((item) => isSelected(item));
    const indeterminate = filterList.some((item) => isSelected(item)) && filterList.some((item) => !isSelected(item));
    return {
      checked,
      indeterminate,
      onPress: (value: boolean) => {
        if (!value) {
          clearAllSelected();
        } else {
          addSelected(filterList);
        }
      },
    };
  }, [orgList, selectedList, clearAllSelected, addSelected]);

  const onScroll = useCallback(() => {
    if (isEndRef.current || orgList.length === ZERO || isPendingRef.current) {
      return;
    }
    const page = pageRef.current + 1;
    pageRef.current = page;
    isPendingRef.current = true;
    getUserList(
      {
        page,
        perPage: pageSize,
        departmentId: currentCrumb,
        ignorePending: ignorePending,
      },
      admin,
    )
      .then(({ list }) => {
        if (!list || list?.length <= 0 || list.length < pageSize) {
          isEndRef.current = true;
        }
        setOrgList((oldList) =>
          pageRef.current === page
            ? oldList.concat(
                convertOrgList({
                  list,
                  checkDepartment,
                }),
              )
            : oldList,
        );
      })
      .catch(() => {
        isEndRef.current = true;
      })
      .finally(() => {
        isPendingRef.current = false;
      });
  }, [orgList, currentCrumb, checkDepartment, pageSize, ignorePending, admin]);

  const initSelect = useCallback(
    (initList?: OrgItemType[]) => {
      setLoading(true);
      if (initList) {
        addSelected(initList, true);
      }
      getOrgList({
        page: DEFAULT_PAGE_CURRENT,
        departmentId: ROOT_DEPARTMENT,
        ignorePending,
      });
    },
    [getOrgList, ignorePending, addSelected],
  );

  const clearOrgList = useCallback(() => {
    pageRef.current = DEFAULT_PAGE_CURRENT;
    isEndRef.current = false;
    setOrgList([]);
  }, []);

  return useMemo(() => {
    return {
      loading: loading,
      searchLoading: search.loading,
      crumbs: crumbs
        ? crumbs.map((item) => ({
            ...item,
            onClick:
              item.id === currentCrumb
                ? undefined
                : () => {
                    clickCrumbsHandler(item.id);
                  },
          }))
        : undefined,
      orgList: orgList.map((item) => {
        const basicItem = {
          ...item,
          checked: selectedList.some((selected) => selected.id === item.id),
        };
        return {
          ...basicItem,
          onPress:
            item.type === OrgType.Department
              ? () => {
                  onPressDepartment(item.id);
                }
              : undefined,
          onCheck: () => {
            onCheckHandler(basicItem);
          },
        };
      }),
      selectedList: selectedList.map((item) => ({
        ...item,
        onCancel: () => {
          cancelSelected(item.id);
        },
      })),
      orgSelectAll,
      clearAllSelected,
      cancelSelected,
      onScroll: debounce(onScroll, DEBOUNCE_INPUT_WAIT),
      initSelect,
      clearOrgList,
      onSearch: search.onSearch,
      clearSearch: search.clearSearch,
      searchResult: search.result
        ? search.result.map((item) => ({
            ...item,
            onCheck: () => {
              onCheckHandler(item);
            },
          }))
        : undefined,
    };
  }, [
    loading,
    search.loading,
    search.onSearch,
    search.clearSearch,
    search.result,
    crumbs,
    orgList,
    selectedList,
    orgSelectAll,
    clearAllSelected,
    cancelSelected,
    onScroll,
    initSelect,
    clearOrgList,
    currentCrumb,
    clickCrumbsHandler,
    onPressDepartment,
    onCheckHandler,
  ]);
}

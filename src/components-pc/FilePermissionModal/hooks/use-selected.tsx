import type { ChangeEventHandler } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import type { OrgItemType } from '@/components/OrgSelectCard/type';
import { OrgType } from '@/components/OrgSelectCard/type';
import { ZERO } from '@/configs/configs';
import { SELECT_BASIC } from '@/contexts/departments/type';
import { formatterOpenSelectedList } from '@/pages/pc/Enterprise/Departments/utils';
import type { User } from '@/service/files-team-settings.type';

import type { ItemId, UpdateData } from './use-permission-setting';
import { useUserSelect } from './use-user-select';

export interface UseSelectedProps {
  currentUsers: User[];
  updateData: UpdateData;
  currentPermissionId: ItemId | null;
}

export function useSelected({ currentPermissionId, currentUsers = [], updateData }: UseSelectedProps) {
  const selectedTitle = '已选择的成员';
  const placeholderText = '搜索成员';
  const pressText = '清空';

  const initialOrgItems = useMemo((): OrgItemType[] => {
    return currentUsers
      .filter((user) => user.checked)
      .map((user) => ({
        id: user.targetId,
        title: user.name,
        avatar: user.avatar,
        email: user.email,
        type: OrgType.User,
        checked: true,
        checkable: true,
      }));
  }, [currentUsers]);

  const onUpdate = useCallback(
    (users: User[]) => {
      if (!currentPermissionId) return;

      const selectedUserMap = new Map<number, User>();
      users.forEach((user) => selectedUserMap.set(user.targetId, user));

      const allUsersMap = new Map<number, User>();
      currentUsers.forEach((user) => allUsersMap.set(user.targetId, user));
      users.forEach((user) => allUsersMap.set(user.targetId, user));

      const mergedUsers = Array.from(allUsersMap.values()).map((user) => ({
        ...user,
        checked: !!selectedUserMap.has(user.targetId),
      }));

      updateData(currentPermissionId, () => ({
        user: mergedUsers,
      }));
    },
    [currentPermissionId, currentUsers, updateData],
  );

  const {
    loading,
    crumbs,
    orgList,
    selectedList,
    orgSelectAll,
    clearAllSelected,
    onScroll,
    onSearch,
    searchResult,
    searchLoading,
    initSelect: originalInitSelect,
  } = useUserSelect({
    pageSize: 50,
    admin: true,
    enableSelectAllEnterprise: true,
    checkDepartment: false,
    onUpdate,
  });

  const [value, setValue] = useState<string>();

  const searchChangeHandler: ChangeEventHandler<HTMLInputElement> = useCallback(
    (event) => {
      const { value } = event.target;
      setValue(value);
      onSearch?.(value);
    },
    [onSearch],
  );
  const initSelect = useCallback(() => {
    // 使用转换后的用户数据初始化选择状态
    if (initialOrgItems.length > 0) {
      originalInitSelect(initialOrgItems);
    } else {
      originalInitSelect();
    }
  }, [originalInitSelect, initialOrgItems]);

  useEffect(() => {
    initSelect();
  }, []);

  return {
    select: {
      loading: loading || searchLoading,
      onSearch,
      title: '',
      placeholder: placeholderText,
      selectAll: orgSelectAll,
      breadcrumb: crumbs
        ? {
            crumbs,
          }
        : undefined,
      search: searchResult
        ? {
            ...SELECT_BASIC,
            data: searchResult,
          }
        : undefined,
      org: {
        ...SELECT_BASIC,
        onScroll,
        data: orgList,
      },
    },
    selected: {
      header: {
        title: (
          <>
            <div>{selectedTitle}</div>
            <>({selectedList?.length ?? ZERO})</>
          </>
        ),
        onPress: clearAllSelected,
        pressText,
        disabled: selectedList?.length === ZERO || loading,
      },
      list: selectedList,
      itemRender: formatterOpenSelectedList,
    },
    searchChangeHandler,
    value,
  };
}

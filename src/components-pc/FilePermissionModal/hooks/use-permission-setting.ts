import { useCallback, useEffect, useMemo, useState } from 'react';

import { getTeamSettings, updateTeamSettings } from '@/service/files-team-settings';
import type { PermissionsInfo, RolePermission } from '@/service/files-team-settings.type';

import { usePermission } from './use-permission';

export type TabList = ReturnType<typeof usePermissionSetting>['tabList'];
export type UpdateData = ReturnType<typeof usePermissionSetting>['updateData'];
export type TabListItem = TabList[number];
export type ItemId = TabListItem['list'][number]['id'];

const createRolePermissions = (values: number[] = [], list?: RolePermission[]) =>
  values.map((targetId) => {
    const item = list?.find((item) => item.targetId === targetId);
    return {
      targetId,
      allow: item ? item.allow : false,
      inherited: item?.inherited || false,
    };
  });

export function usePermissionSetting() {
  const { permissionTitleMap, permissionCategoryOptionMap, defaultPermission } = usePermission();

  const [data, setData] = useState<PermissionsInfo>({} as PermissionsInfo);
  const [changed, setChanged] = useState(false);
  const [loadingState, setLoadingState] = useState<boolean>(true);

  const getTab = useCallback(
    (key: keyof typeof permissionCategoryOptionMap, info: PermissionsInfo) => {
      return {
        key,
        label: permissionCategoryOptionMap[key].name,
        list: permissionCategoryOptionMap[key].content.map((id) => {
          const item = defaultPermission[id];
          return {
            id: id,
            name: permissionTitleMap[id].enterprise,
            teamRole: info[id].team_role,
            fileRole: createRolePermissions(item.fileRole.fileRole.values, info[id]?.file_role),
            fileRoleOutsider: createRolePermissions(
              item.fileRole.fileRoleOutsider.values,
              info[id]?.file_role_outsider,
            ),
            users:
              info[id]?.user?.map((item) => ({
                ...item,
                checked: item?.checked === undefined ? true : item?.checked,
              })) || [],
          };
        }),
      };
    },
    [permissionCategoryOptionMap, permissionTitleMap, defaultPermission],
  );

  const tabList = useMemo(() => {
    if (Object.keys(data).length === 0) {
      return [];
    }
    return [getTab('CollabPermission', data), getTab('ControlPermission', data)];
  }, [data, getTab]);

  const getSettings = useCallback(async () => {
    try {
      setLoadingState(true);
      const res = await getTeamSettings();
      setData(res.data.permissions);
      setLoadingState(false);
    } catch (error) {
      console.error('Failed to load team settings:', error);
      setLoadingState(false);
    }
  }, []);

  const updateData = useCallback(
    (
      id: ItemId,
      updater: Partial<PermissionsInfo[ItemId]> | ((prev: PermissionsInfo[ItemId]) => Partial<PermissionsInfo[ItemId]>),
    ) => {
      setChanged(true);
      setData((pre) => ({
        ...pre,
        [id]: {
          ...pre[id],
          ...(typeof updater === 'function' ? updater(pre[id]) : updater),
        },
      }));
    },
    [],
  );

  const onSave = useCallback(() => {
    updateTeamSettings({ permissions: data, allowLoosen: true });
  }, [data]);

  useEffect(() => {
    getSettings();
  }, [getSettings]);

  return {
    tabList,
    updateData,
    changed,
    isLoading: loadingState,
    onSave,
  };
}

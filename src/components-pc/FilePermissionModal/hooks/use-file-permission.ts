import { useCallback, useMemo, useState } from 'react';

import type { ItemId, TabListItem } from './use-permission-setting';
import { usePermissionSetting } from './use-permission-setting';

export enum ViewType {
  MAIN = 'main',
  USER_LIST = 'userList',
  USER_ADD = 'userAdd',
}

export const VIEW_TITLES = {
  [ViewType.MAIN]: '高级权限设置',
  [ViewType.USER_LIST]: '成员列表',
  [ViewType.USER_ADD]: '添加指定成员',
} as const;

export type CollapseListItem = {
  key: string;
  label: string;
};

const NAVIGATION_MAP: Record<ViewType, ViewType | null> = {
  [ViewType.MAIN]: null,
  [ViewType.USER_LIST]: ViewType.MAIN,
  [ViewType.USER_ADD]: ViewType.USER_LIST,
};

const FORWARD_NAVIGATION_MAP: Record<ViewType, ViewType | null> = {
  [ViewType.MAIN]: ViewType.USER_LIST,
  [ViewType.USER_LIST]: ViewType.USER_ADD,
  [ViewType.USER_ADD]: null,
};

export function useFilePermission() {
  const { tabList, updateData, changed, isLoading, onSave } = usePermissionSetting();
  const [currentView, setCurrentView] = useState<ViewType>(ViewType.MAIN);
  const [currentPermissionId, setCurrentPermissionId] = useState<ItemId | null>(null);

  // 获取当前选择权限项的用户数据
  const currentUsers = useMemo(() => {
    if (!currentPermissionId) {
      return [];
    }

    for (const tab of tabList) {
      const item = tab.list.find((item) => item.id === currentPermissionId);
      if (item?.users) {
        return item.users;
      }
    }

    return [];
  }, [tabList, currentPermissionId]);

  const title = VIEW_TITLES[currentView];
  const showGoBack = currentView !== ViewType.MAIN;

  const goBack = useCallback(() => {
    const previousView = NAVIGATION_MAP[currentView];
    if (previousView) {
      setCurrentView(previousView);
    }
  }, [currentView]);

  const goNext = useCallback(
    (permissionId?: ItemId) => {
      const nextView = FORWARD_NAVIGATION_MAP[currentView];
      if (nextView) {
        setCurrentView(nextView);
        if (permissionId) {
          setCurrentPermissionId(permissionId);
        }
      }
    },
    [currentView],
  );

  const transformCollapseList = useCallback(
    (list: TabListItem['list']): CollapseListItem[] =>
      list.map(({ id, name }) => ({
        key: id,
        label: name,
      })),
    [],
  );

  return {
    title,
    currentView,
    showGoBack,
    goBack,
    goNext,
    tabList,
    updateData,
    transformCollapseList,
    currentUsers,
    changed,
    isLoading,
    onSave,
    currentPermissionId,
  };
}

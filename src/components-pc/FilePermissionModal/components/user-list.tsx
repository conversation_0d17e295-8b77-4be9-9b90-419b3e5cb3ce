import { Button, Checkbox } from 'antd';

import { ReactComponent as EmptyUserListIcon } from '@/assets/images/svg/empty-user-list.svg';
import { ReactComponent as PlusIcon } from '@/assets/images/svg/plus-circle.svg';

import type { UserListProps } from '../hooks/use-user-list';
import { useUserList } from '../hooks/use-user-list';
import { TooltipIcon } from './tooltip-icon';
import styles from './user-list.less';

export function UserList(props: UserListProps) {
  const {
    titleText,
    addText,
    toolTips,
    checkText,
    checkAll,
    indeterminate,
    onChange,
    onCheckAllChange,
    onClickNext,
    checkedList,
    list,
  } = useUserList(props);

  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <span className={styles.titleText}>
          {titleText}
          {checkedList.length > 0 && `(${checkedList.length})`}
          <TooltipIcon className={styles.titleIcon} height={14} toolTips={toolTips} width={14} />
        </span>
        <Button size="small" type="link" onClick={onClickNext}>
          <PlusIcon height={16} width={16} />
          {addText}
        </Button>
      </div>
      <div className={styles.list}>
        {list.length === 0 ? (
          <div className={styles.emptyList}>
            <EmptyUserListIcon height={72} width={75} />
          </div>
        ) : (
          <>
            <div className={styles.checkAllItem}>
              <Checkbox checked={checkAll} indeterminate={indeterminate} onChange={onCheckAllChange}>
                {checkText}
              </Checkbox>
            </div>
            {list.map((user) => (
              <div key={user.targetId} className={styles.listItem}>
                <Checkbox
                  checked={user.checked}
                  className={styles.checkbox}
                  value={user.targetId.toString()}
                  onChange={(e) => onChange(e.target.checked, user.targetId)}
                >
                  <div className={styles.userInfo}>
                    <div className={styles.userAvatarName}>
                      <img alt={user.name} className={styles.avatar} src={user.avatar} />
                      <div className={styles.userName}>{user.name}</div>
                    </div>
                    <div className={styles.userEmail}>{user.email}</div>
                  </div>
                </Checkbox>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
}

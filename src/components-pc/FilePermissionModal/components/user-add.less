.container {
  display: flex;
  height: 100%;
  border-radius: 4px 0 0 4px;
  border: 1px solid var(---Divider-Lightertransparency-10, rgba(65, 70, 75, 10%));
}

.left {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  flex: 1;
  border-right: 1px solid var(---Divider-Lightertransparency-10, rgba(65, 70, 75, 10%));
  overflow: hidden;
}

.search {
  margin-bottom: 8px;
}

.right {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.selectedList {
  overflow-y: auto;
}

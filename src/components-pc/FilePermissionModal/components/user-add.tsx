import { Input } from 'antd';

import { OrgSelect } from '@/components/OrgSelectCard/OrgSelect';
import { OrgSelectedEmpty } from '@/components/OrgSelectCard/OrgSelectedEmpty';
import { OrgSelectedHeader } from '@/components/OrgSelectCard/OrgSelectedHeader';

import type { UseSelectedProps } from '../hooks/use-selected';
import { useSelected } from '../hooks/use-selected';
import styles from './user-add.less';

export function UserAdd(props: UseSelectedProps) {
  const { select, selected, searchChangeHandler, value } = useSelected(props);

  const { onSearch, placeholder, ...org } = select;
  const { list, header, itemRender } = selected;

  return (
    <div className={styles.container}>
      <div className={styles.left}>
        <Input.Search
          className={styles.search}
          placeholder={placeholder}
          value={value}
          onChange={searchChangeHandler}
          onSearch={onSearch}
        />
        <OrgSelect {...org} />
      </div>
      <div className={styles.right}>
        <OrgSelectedHeader {...header} />
        {Array.isArray(list) && list.length > 0 ? (
          <div className={styles.selectedList}>{list.map((item) => itemRender(item))}</div>
        ) : (
          <OrgSelectedEmpty />
        )}
      </div>
    </div>
  );
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleText {
  display: flex;
  align-items: center;
  color: var(---Secondary60, rgba(65, 70, 75, 60%));
  font-size: 12px;
  line-height: 20px;
}

.titleBtn {
  color: var(---Disabled30, rgba(65, 70, 75, 30%));
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}

.titleIcon {
  margin-left: 4px;
}

.list {
  padding: 4px 0;
  margin-top: 8px;
  border-radius: 2px;
  border: 1px solid var(---Divider-Lightertransparency-10, rgba(65, 70, 75, 10%));
  display: flex;
  height: 100%;
  flex-direction: column;
  align-items: flex-start;
  overflow-y: auto;
}

.emptyList {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}

.checkAllItem {
  padding: 8px;
  width: 100%;

  .ant-checkbox-wrapper {
    font-size: 14px;
    color: var(---Secondary80, rgba(65, 70, 75, 80%));
  }
}

.listItem {
  padding: 8px 24px 8px 8px;
  width: 100%;

  &:hover {
    background-color: var(---Background-Hover, rgba(65, 70, 75, 4%));
  }

  .ant-checkbox-wrapper {
    width: 100%;

    .ant-checkbox {
      align-self: flex-start;
      margin-top: 2px;
    }
  }
}

.userInfo {
  display: flex;
  flex: 1;
  align-items: center;
  margin-left: 8px;
  width: 100%;
}

.userAvatarName {
  display: flex;
  flex-direction: row;
  flex: 1;
  align-items: center;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.userName {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: var(---Secondary90, rgba(65, 70, 75, 90%));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.userEmail {
  font-size: 12px;
  line-height: 16px;
  color: var(---Secondary60, rgba(65, 70, 75, 60%));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checkbox {
  display: flex;
  align-items: center;

  :global(.ant-checkbox-label) {
    flex: 1;
  }
}

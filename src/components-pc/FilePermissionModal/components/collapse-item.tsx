import { Avatar } from 'antd';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import UserCardPopover from '@/components/UserCardPopover';
import { useFormatMessage } from '@/modules/Locale';

import { useCollapseItem } from '../hooks/use-collapse-item';
import type { ItemId, TabListItem, UpdateData } from '../hooks/use-permission-setting';
import { CheckboxList } from './checkbox-list';
import { CheckboxWithDropdown } from './checkbox-with-dropdown';
import styles from './collapse-item.less';

export type CollapseItemProps = TabListItem['list'][number] & {
  onViewAll: (permissionId: ItemId) => void;
  onUpdate: UpdateData;
};

export function CollapseItem({ onViewAll, onUpdate, ...rest }: CollapseItemProps) {
  const labelText = '指定成员';
  const showAllText = '查看全部';
  const noCollaboratorText = useFormatMessage('ShareCollaboration.noCollaborator');

  const { admin, creator, roleList, goUserList, users } = useCollapseItem(rest, onUpdate, onViewAll);

  return (
    <div className={styles.container}>
      <div className={styles.checkboxSection}>
        <CheckboxList admin={admin || undefined} creator={creator || undefined} />
        {roleList.map(({ key, data, ...props }) => (
          <CheckboxWithDropdown key={key} data={Array.isArray(data) ? data : undefined} {...props} />
        ))}
      </div>

      <div className={styles.membersSection}>
        <div className={styles.membersLeft}>
          <span className={styles.membersLabel}>{labelText}</span>
          {users.length > 0 ? (
            <Avatar.Group max={{ count: 10, style: { width: '24px', height: '24px' } }}>
              {users.map((user) => (
                <UserCardPopover key={user.targetId} userId={user.targetId}>
                  <Avatar className={styles.avatar} size={24} src={user.avatar} />
                </UserCardPopover>
              ))}
            </Avatar.Group>
          ) : (
            <div className={styles.noDataCollaborator}>
              <NoDataIcon />
              <span>{noCollaboratorText}</span>
            </div>
          )}
        </div>
        <div className={styles.viewAllButton} onClick={goUserList}>
          <span className={styles.viewAllText}>
            {showAllText}
            {users.length > 0 ? `(${users.length})` : ''}
          </span>
          <ArrowRight />
        </div>
      </div>
    </div>
  );
}

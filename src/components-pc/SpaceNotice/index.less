.spaceNotice {
  width: 338px;
  height: 100%;
  display: flex;
  border-left: 1px solid var(--theme-basic-color-black);
  flex-direction: column;
  flex: 0 0 auto;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 30px;
    color: var(--theme-text-color-secondary);

    h4 {
      font-size: 13px;
      line-height: 20px;
      font-weight: 500;
      flex: 1 1 auto;
      color: var(--theme-text-color-default);
    }

    :global {
      .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
        background: transparent;
        color: var(--theme-button-icon-color-hover);
      }
    }
  }

  .content {
    flex: 1 0 auto;
    padding: 0 30px;
    overflow: hidden;
  }

  .emptyContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 30px;

    div {
      color: var(--theme-text-color-secondary);
    }
  }

  .emptyContentInner {
    margin-top: -100%;

    div {
      color: var(--theme-text-color-secondary);
    }
  }

  .emptyIcon {
    margin-bottom: 16px;
  }

  .thumbnail {
    width: 264px;
    height: 264px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--theme-basic-color-bg-gray);

    :global {
      .adm-image {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.timelines {
  padding: 0 30px;
  overflow-y: scroll;
}

.timelinesTime {
  display: block;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 20px;
}

import { Typography } from 'antd';
import React, { useCallback } from 'react';

interface Props {
  content: string;
}

/** 含链接的字符串转化为可点击的链接 */
export const LinkifyText: React.FC<Props> = (props) => {
  const { content } = props;

  const handleLinkClick = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
    e.stopPropagation();
    e.preventDefault();

    const href = e.currentTarget.href;
    try {
      // 验证 URL 格式
      const url = new URL(href);
      window.open(url.toString(), '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('无效的 URL:', href);
    }
  }, []);

  if (!content) return null;

  const URL_REGEX = /(https?:\/\/|ftp:\/\/|www\.)[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?/gi;

  // 获取所有匹配项及其位置
  const matches: RegExpMatchArray[] = [];
  let match;
  while ((match = URL_REGEX.exec(content)) !== null) {
    matches.push(match);
  }

  // 如果没有匹配到 URL，直接返回原始文本
  if (matches.length === 0) {
    return <Typography.Text>{content}</Typography.Text>;
  }

  const elements: React.ReactNode[] = [];
  let lastIndex = 0;

  matches.forEach((match, index) => {
    const url = match[0];
    const startIndex = match.index!;

    // 添加 URL 之前的文本
    if (startIndex > lastIndex) {
      elements.push(<React.Fragment key={`text-${index}`}>{content.substring(lastIndex, startIndex)}</React.Fragment>);
    }

    // 添加链接
    const href = url.startsWith('www.') ? `https://${url}` : url;
    elements.push(
      <Typography.Link
        key={`link-${index}`}
        href={href}
        rel="noopener noreferrer"
        target="_blank"
        onClick={handleLinkClick}
      >
        {url}
      </Typography.Link>,
    );

    lastIndex = startIndex + url.length;
  });

  // 添加剩余文本
  if (lastIndex < content.length) {
    elements.push(<React.Fragment key={`text-end`}>{content.substring(lastIndex)}</React.Fragment>);
  }

  return <Typography.Text>{elements}</Typography.Text>;
};

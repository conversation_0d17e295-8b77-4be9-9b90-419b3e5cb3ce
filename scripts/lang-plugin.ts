/**
 * 这个插件会在HTML中注入JavaScript代码，动态读取cookie中的SM_LOCALE值并设置html标签的lang属性
 * 用作处理sdk内部的语言切换
 */
import type { IApi } from 'umi';

export default (api: IApi) => {
  api.describe({
    key: 'set html lang attribute from locale cookie',
  });

  api.modifyHTML(($) => {
    // 在head标签中添加JavaScript代码来动态设置lang属性
    $('head').append(`
      <script>
        (function() {
          // 读取cookie的辅助函数
          function getCookie(name) {
            const value = "; " + document.cookie;
            const parts = value.split("; " + name + "=");
            if (parts.length == 2) return parts.pop().split(";").shift();
            return null;
          }
          
          // 设置cookie的辅助函数
          function setCookie(name, value, days) {
            let expires = "";
            if (days) {
              const date = new Date();
              date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
              expires = "; expires=" + date.toUTCString();
            }
            document.cookie = name + "=" + (value || "") + expires + "; path=/";
          }
          
          // 获取locale的值
          let locale = getCookie('locale');
          
          // 设置默认值
          if (!locale) {
            locale = 'zh-CN';
          }
          
          // 将locale转换为6+2 sdk能认识的格式
          const langCode = locale === 'zh-CN' ? 'zh-CN' : 'en';
          
          // 设置html标签的lang属性（初始6大套件sdk）
          document.documentElement.setAttribute('lang', langCode);
          
          // 同时设置SM_LOCALE cookie （白板脑图）
          setCookie('SM_LOCALE', langCode, 365);
          
        })();
      </script>
    `);

    return $;
  });
};

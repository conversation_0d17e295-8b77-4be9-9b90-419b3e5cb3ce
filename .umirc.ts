import { defineConfig } from 'umi';

const isMobilePlatform = process.env.PLATFORM === 'mobile';

const HOST = 'co-dev-14.shimorelease.com';
const ORIGIN = `https://${HOST}`;
// const WS_ORIGIN = `https://ws.${HOST}`;
const WS_ORIGIN = `ws://localhost:8000`;
const OBS_UPLOAD_ORIGIN = 'smdev-svc-drive.obs.cn-north-4.myhuaweicloud.com';

const EditorPaths = ['/doc', '/spreadsheet', '/slides'];

const ProxyRules = [
  '/api/v1',
  '/lizard-api',
  '/lizard-one',
  '/perception',
  '/panda-api',
  '/static',
  '/agreements',
  '/create',
  '/logout',
  '/loginByPassword',
  '/pricing',
  '/sdk',
  '/edit',
  '/comment-api',
  '/aioa',
  '/uploader/f',
  '/uploader',
  '/minio',
  '/sdk',
  /^\/\w{16}\/collaborators/, // 协作者
  ...EditorPaths,
  `/${OBS_UPLOAD_ORIGIN}`,
];

function getProxyContext(path: string) {
  for (const rule of ProxyRules) {
    if (typeof rule === 'string' && new RegExp(`^${rule}(?!\\w)`).test(path)) {
      return true;
    } else if (rule instanceof RegExp && rule.test(path)) {
      return true;
    }
  }

  return false;
}

enum HandoverRouteUrl {
  FileHandoverModal = '/organization/file_handover_modal',
  FileHandoverHistoryModal = '/organization/file_handover_history_modal',
  FileHandoffManage = '/organization/handoff/status/:id', // 管理文件交接
  FileHandoverManage = '/organization/handover/status/:id',
  FileHandoff = '/organization/handoff/:id', // 发起文件交接
  FileHandover = '/organization/handover/:id',
  Efficiency = '/organization/efficiency',
  Members = '/organization/members',
}

const HandoverRoutes = [
  { path: HandoverRouteUrl.FileHandoff, component: 'pc/Members/components/HandoverModal/FileHandover' },
  { path: HandoverRouteUrl.FileHandover, component: 'pc/Members/components/HandoverModal/FileHandover' },
  { path: HandoverRouteUrl.FileHandoffManage, component: 'pc/Members/components/HandoverModal/FileHandover' },
  { path: HandoverRouteUrl.FileHandoverManage, component: 'pc/Members/components/HandoverModal/FileHandover' },
  {
    path: HandoverRouteUrl.FileHandoverHistoryModal,
    component:
      'pc/Members/components/HandoverModal/HandoverModalPage/FileHandoverHistoryModal/FileHandoverHistoryModal',
  },
  {
    path: HandoverRouteUrl.FileHandoverModal,
    component: 'pc/Members/components/HandoverModal/HandoverModalPage/FileHandoverModal/FileHandoverModal',
  },
];

type UmiConfigRoutes = Parameters<typeof defineConfig>[0]['routes'];
const CommonRoutes = [
  { path: '__DRIVE__/config', component: 'common/DriveConfig' },
  { path: '/docs/:guid/*', component: 'common/Editor' },
  { path: '/docx/:guid/*', component: 'common/Editor' },
  { path: '/sheets/:guid/*', component: 'common/Editor' },
  { path: '/presentation/:guid/*', component: 'common/Editor' },
  { path: '/tables/:guid/*', component: 'common/Editor' },
  { path: '/forms/:guid/fill-form', component: 'common/Editor' },
  { path: '/forms/:guid/*', component: 'common/Editor' },
  { path: '/boards/:guid/*', component: 'common/Editor' }, // 白板
  { path: '/mindmaps/:guid/*', component: 'common/Editor' },
  { path: '/files/:guid/*', component: 'pc/Preview' }, // 预览文件
  { path: '/login', component: 'common/Login' },
  { path: '/ldap/login', component: 'common/LdapLogin' }, // 自定义登录页面 - 使用前端路由路径
  { path: '/error', component: 'common/Error' },
] satisfies UmiConfigRoutes;

const PcRoutes = [
  ...CommonRoutes,
  { path: '/', redirect: '/login' }, // DRIVE-476
  { path: '/desktop', component: 'pc/Desktop' },
  { path: '/folder/:guid/*', component: 'pc/Desktop' },
  { path: '/space', component: 'pc/Space' },
  { path: '/space/:guid/*', component: 'pc/Desktop' },
  { path: '/recent', component: 'pc/Recent' },
  { path: '/share', component: 'pc/Share' },
  { path: '/favorites', component: 'pc/Favorites' },
  { path: '/trash', component: 'pc/Trash' },
  { path: '/enterprise/members', component: 'pc/Members' },
  { path: '/profile/accountinfo', component: 'pc/Profile/AccountInfo' },
  { path: '/profile/preference', component: 'pc/Profile/Preference' },
  { path: '/enterprise/trash', component: 'pc/Enterprise/Trash' },
  { path: '/enterprise/template', component: 'pc/Enterprise/Template' },
  { path: '/forbidden', component: 'pc/Forbidden' },
  { path: '/enterprise/settings', component: 'pc/Enterprise/Settings' }, // 企业设置
  { path: '/enterprise/audit', component: 'pc/Enterprise/Audit' },
  { path: '/workActivity', component: 'pc/WorkActivity' },
  { path: '/enterprise/efficiency', component: 'pc/EfficiencyPanel' },
  { path: '/file-invite/:id', component: 'pc/File-Invite' },
  ...HandoverRoutes,
  { path: '/enterprise/fileSecurityAlert', component: 'pc/Enterprise/FileSecurityAlert' },
] satisfies UmiConfigRoutes;

const MobileRoutes = [
  ...CommonRoutes,
  { path: '/', redirect: '/recent' },
  { path: '/search', component: 'mobile/Search' },
  {
    path: '/my',
    component: 'mobile/My',
    routes: [
      {
        path: 'account-management',
        component: 'mobile/My/AccountManagement',
        routes: [{ path: 'update-user-name', component: 'mobile/My/UpdateUserName' }],
      },
      { path: 'night-mode', component: 'mobile/My/NightMode' },
      { path: 'language', component: 'mobile/My/Language' },
      { path: 'recycle-bin', component: 'mobile/My/RecycleBin' },
    ],
  },
  { path: '/desktop', component: 'mobile/Desktop' },
  { path: '/space', component: 'mobile/Space' },
  { path: '/space/:guid/*', component: 'mobile/Desktop' },
  { path: '/recent', component: 'mobile/Recent' },
  { path: '/favorites', component: 'mobile/Favorites' },
  { path: '/created', component: 'mobile/Created' },
  { path: '/share', component: 'mobile/Share' },
  { path: '/folder/:guid/*', component: 'mobile/Desktop' },
  { path: '/notification', component: 'mobile/Notification' },
  { path: '/templates/:id', component: 'mobile/Templates' },
  { path: '/file-invite/:id', component: 'mobile/File-Invite' },
] satisfies UmiConfigRoutes;

export default defineConfig({
  base: '/',
  // publicPath: process.env.WEBPACK_PUBLIC_PATH,
  publicPath: process.env.NODE_ENV === 'development' ? 'http://localhost:8000/' : process.env.WEBPACK_PUBLIC_PATH,
  jsMinifier: 'esbuild', // 关键：指定使用 esbuild
  jsMinifierOptions: {
    // esbuild 专属配置
    minify: true,
    target: 'es2020',
    format: 'iife', // 强制 IIFE 格式
    minifyIdentifiers: true,
    minifySyntax: true,
    minifyWhitespace: true,
  },
  routes: isMobilePlatform ? MobileRoutes : PcRoutes,
  // 兼容性要求参考：https://shimo.im/docs/WkMZMxtQ2LQVUgF2
  targets: {
    chrome: 78,
    edge: 84,
    safari: '13.6',
  },
  hash: true,
  codeSplitting: {
    jsStrategy: 'depPerChunk',
  },
  npmClient: 'yarn',
  proxy: {
    context: getProxyContext, // 不支持的界面需要直接跳转到 lizard 项目中
    target: ORIGIN,
    secure: false,
    changeOrigin: true,
    cookieDomainRewrite: {
      [`.${HOST}`]: '',
      [`${HOST}`]: '',
    },
    onProxyRes: (proxyRes: any, req: any, res: any) => {
      //  后端 register 和 login 接口 set cookie 时设置了 Secure，用本地 ip 访问 dev server 时带 Secure 标识会导致 set cookie 失败
      if (
        ['/api/v1/auth/password/register', '/api/v1/auth/password/login', '/api/v1/auth/password/login'].includes(
          req.originalUrl,
        )
      ) {
        const setCookieHeader = proxyRes.headers['set-cookie'];
        if (typeof setCookieHeader === 'string') {
          proxyRes.headers['set-cookie'] = setCookieHeader.replace('Secure', '').replace('SameSite=None', '');
        } else if (Array.isArray(setCookieHeader)) {
          for (let index = 0; index < setCookieHeader.length; index++) {
            const cookie = setCookieHeader[index];
            setCookieHeader[index] = cookie.replace('Secure', '').replace('SameSite=None', '');
          }
        }
      }
    },
    headers: {
      Referer: ORIGIN,
      Origin: ORIGIN,
      'X-Request-From': 'Lizard-View-Proxy',
    },
  },
  plugins: [
    '@umijs/plugins/dist/locale',
    '@umijs/plugins/dist/styled-components',
    './scripts/build-meta-plugin.ts',
    './scripts/externals-plugin.ts',
    './scripts/prefetch-plugin.ts',
    './scripts/injects-plugin.ts',
    './scripts/lang-plugin.ts',
  ],
  locale: {
    default: 'zh-CN',
    antd: true, // 如果使用 antd
  },
  copy: [
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
  ],
  externals:
    process.env.NODE_ENV === 'production'
      ? {
          react: 'window.React',
          'react-dom': 'window.ReactDOM',
          lodash: 'window._',
          dayjs: 'window.dayjs',
        }
      : {},
  styledComponents: {
    babelPlugin: {
      // 生成有语义的类名便于调试
      displayName: process.env.NODE_ENV === 'development',
      // 防止 CSS 类名冲突
      namespace: 'drive',
      // 生成更小的 CSS 文件
      minify: process.env.NODE_ENV === 'production',
    },
  },
  define: {
    'process.env.PLATFORM': process.env.PLATFORM,
    'process.env.SOCKET_SERVER': WS_ORIGIN,
  },
});
